<?php
// verify_arabic.php - <PERSON><PERSON><PERSON> to verify Arabic migration was successful
require_once 'db_connect.php';

try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "<h2>Arabic Migration Verification</h2>\n";
    
    // Check categories
    echo "<h3>Categories with Arabic names:</h3>\n";
    $categories = $conn->query("SELECT id, name_en, name_ar FROM categories ORDER BY id")->fetchAll();
    echo "<table border='1'>\n";
    echo "<tr><th>ID</th><th>English</th><th>Arabic</th></tr>\n";
    foreach ($categories as $cat) {
        echo "<tr><td>{$cat['id']}</td><td>{$cat['name_en']}</td><td>{$cat['name_ar']}</td></tr>\n";
    }
    echo "</table>\n";
    
    // Check products
    echo "<h3>Products with Arabic descriptions:</h3>\n";
    $products = $conn->query("SELECT id, name, description_en, description_ar FROM products LIMIT 5")->fetchAll();
    echo "<table border='1'>\n";
    echo "<tr><th>ID</th><th>Name</th><th>English Desc</th><th>Arabic Desc</th></tr>\n";
    foreach ($products as $prod) {
        echo "<tr><td>{$prod['id']}</td><td>{$prod['name']}</td><td>{$prod['description_en']}</td><td>{$prod['description_ar']}</td></tr>\n";
    }
    echo "</table>\n";
    
    echo "<p><strong>✅ Migration verification complete!</strong></p>\n";
    echo "<p>Arabic columns are present and populated with data.</p>\n";
    
} catch (Exception $e) {
    echo "<p><strong>❌ Error:</strong> " . $e->getMessage() . "</p>\n";
}
?>
