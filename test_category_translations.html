<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Traductions des Catégories</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .language-switch {
            margin-bottom: 20px;
            text-align: center;
        }
        .lang-btn {
            padding: 10px 20px;
            margin: 0 5px;
            border: 2px solid #D32F2F;
            background: white;
            color: #D32F2F;
            cursor: pointer;
            border-radius: 5px;
            font-weight: bold;
        }
        .lang-btn.active {
            background: #D32F2F;
            color: white;
        }
        .category-test {
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f9f9f9;
        }
        .category-name {
            font-size: 18px;
            font-weight: bold;
            color: #D32F2F;
            margin-bottom: 5px;
        }
        .translation-key {
            font-size: 12px;
            color: #666;
            font-family: monospace;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test des Traductions des Catégories</h1>
        
        <div class="language-switch">
            <button class="lang-btn" data-lang="en">English</button>
            <button class="lang-btn active" data-lang="ar">العربية</button>
        </div>

        <div class="category-test">
            <div class="category-name" data-i18n="categories.lighters_gas">Lighters & Gas</div>
            <div class="translation-key">Clé: categories.lighters_gas</div>
        </div>

        <div class="category-test">
            <div class="category-name" data-i18n="categories.toothbrushes">Toothbrushes</div>
            <div class="translation-key">Clé: categories.toothbrushes</div>
        </div>

        <div class="category-test">
            <div class="category-name" data-i18n="categories.adhesive_glue">Adhesive Glue</div>
            <div class="translation-key">Clé: categories.adhesive_glue</div>
        </div>

        <div class="category-test">
            <div class="category-name" data-i18n="categories.baby_products">Baby Products</div>
            <div class="translation-key">Clé: categories.baby_products</div>
        </div>

        <div class="category-test">
            <div class="category-name" data-i18n="categories.insecticides">Insecticides</div>
            <div class="translation-key">Clé: categories.insecticides</div>
        </div>

        <div id="status" class="status success">
            ✅ Toutes les traductions sont chargées et fonctionnent correctement !
        </div>
    </div>

    <script>
        // Current language state
        let currentLang = 'ar';

        // Translation data (same as in script.js)
        const translations = {
            en: {
                'categories.lighters_gas': 'Lighters & Gas',
                'categories.toothbrushes': 'Toothbrushes',
                'categories.adhesive_glue': 'Adhesive Glue',
                'categories.baby_products': 'Baby Products',
                'categories.insecticides': 'Insecticides'
            },
            ar: {
                'categories.lighters_gas': 'الولاعات والغاز',
                'categories.toothbrushes': 'فرش الأسنان',
                'categories.adhesive_glue': 'الغراء اللاصق',
                'categories.baby_products': 'منتجات الأطفال',
                'categories.insecticides': 'المبيدات الحشرية'
            }
        };

        // Language switching functionality
        function updateLanguage(lang) {
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.dataset.i18n;
                if (translations[lang] && translations[lang][key]) {
                    element.textContent = translations[lang][key];
                } else {
                    console.error(`Translation missing for key: ${key} in language: ${lang}`);
                    document.getElementById('status').className = 'status error';
                    document.getElementById('status').textContent = `❌ Traduction manquante pour: ${key}`;
                }
            });

            // Update document direction
            document.documentElement.lang = lang;
            document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
            document.body.style.direction = lang === 'ar' ? 'rtl' : 'ltr';
            document.body.style.textAlign = lang === 'ar' ? 'right' : 'left';
        }

        function updateActiveLanguageButton(activeBtn) {
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.toggle('active', btn === activeBtn);
            });
        }

        // Event listeners
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const newLang = e.target.dataset.lang;
                if (newLang !== currentLang) {
                    currentLang = newLang;
                    updateLanguage(newLang);
                    updateActiveLanguageButton(e.target);
                }
            });
        });

        // Initialize
        updateLanguage(currentLang);
    </script>
</body>
</html>
