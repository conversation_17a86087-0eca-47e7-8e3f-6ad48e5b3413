<?php
// fix_security.php
require_once 'db_connect.php';

try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "Début de l'amélioration de la sécurité...\n";
    
    // Hasher le mot de passe admin existant
    $new_password_hash = password_hash('admin123', PASSWORD_DEFAULT);
    
    $sql = "UPDATE admins SET password = :password WHERE username = 'admin'";
    $stmt = $conn->prepare($sql);
    $stmt->execute([':password' => $new_password_hash]);
    
    echo "Mot de passe admin hashé avec succès!\n";
    echo "Le mot de passe reste 'admin123' mais est maintenant sécurisé.\n";
    
    // Vérifier la mise à jour
    $stmt = $conn->prepare("SELECT username, password FROM admins WHERE username = 'admin'");
    $stmt->execute();
    $admin = $stmt->fetch();
    
    if ($admin && password_verify('admin123', $admin['password'])) {
        echo "Vérification réussie: Le mot de passe hashé fonctionne correctement.\n";
    } else {
        echo "ERREUR: Le mot de passe hashé ne fonctionne pas correctement!\n";
    }
    
    echo "\nAmélioration de la sécurité terminée!\n";
    
} catch (Exception $e) {
    echo "Erreur lors de l'amélioration de la sécurité: " . $e->getMessage() . "\n";
    file_put_contents('debug.log', "fix_security.php: {$e->getMessage()}\n", FILE_APPEND);
}
?>
