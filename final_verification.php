<?php
// final_verification.php - Script de vérification finale pour la bidirectionnalité
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vérification Finale - Bidirectionnalité</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Vérification Finale - Bidirectionnalité Arabe/Anglais</h1>
        
        <?php
        require_once 'db_connect.php';
        
        $checks = [];
        $errors = [];
        
        try {
            $db = Database::getInstance();
            $conn = $db->getConnection();
            
            // Vérification 1: Colonnes Arabes dans la base de données
            echo "<div class='test-section'>";
            echo "<h2>✅ Test 1: Structure de la base de données</h2>";
            
            $tables = ['categories', 'products'];
            foreach ($tables as $table) {
                $columns = $conn->query("SHOW COLUMNS FROM $table")->fetchAll();
                $hasArabic = false;
                foreach ($columns as $col) {
                    if (strpos($col['Field'], '_ar') !== false) {
                        $hasArabic = true;
                        break;
                    }
                }
                if ($hasArabic) {
                    echo "<div class='status success'>✅ Table '$table' contient des colonnes arabes</div>";
                } else {
                    echo "<div class='status error'>❌ Table '$table' ne contient pas de colonnes arabes</div>";
                    $errors[] = "Colonnes arabes manquantes dans $table";
                }
            }
            echo "</div>";
            
            // Vérification 2: Données arabes
            echo "<div class='test-section'>";
            echo "<h2>✅ Test 2: Données arabes dans la base</h2>";
            
            $categories = $conn->query("SELECT name_en, name_ar FROM categories LIMIT 3")->fetchAll();
            echo "<table>";
            echo "<tr><th>Anglais</th><th>Arabe</th><th>Status</th></tr>";
            foreach ($categories as $cat) {
                $hasArabic = !empty($cat['name_ar']) && preg_match('/[\x{0600}-\x{06FF}]/u', $cat['name_ar']);
                $status = $hasArabic ? "✅ OK" : "❌ Manquant";
                $class = $hasArabic ? "success" : "error";
                echo "<tr class='$class'><td>{$cat['name_en']}</td><td>{$cat['name_ar']}</td><td>$status</td></tr>";
            }
            echo "</table>";
            echo "</div>";
            
            // Vérification 3: Fichiers de traduction
            echo "<div class='test-section'>";
            echo "<h2>✅ Test 3: Fichiers de traduction</h2>";
            
            $files = ['script.js', 'translations.php', 'index.php'];
            foreach ($files as $file) {
                if (file_exists($file)) {
                    $content = file_get_contents($file);
                    $hasArabic = preg_match('/[\x{0600}-\x{06FF}]/u', $content);
                    if ($hasArabic) {
                        echo "<div class='status success'>✅ $file contient du texte arabe</div>";
                    } else {
                        echo "<div class='status error'>❌ $file ne contient pas de texte arabe</div>";
                        $errors[] = "Pas de texte arabe dans $file";
                    }
                } else {
                    echo "<div class='status error'>❌ $file n'existe pas</div>";
                    $errors[] = "Fichier $file manquant";
                }
            }
            echo "</div>";
            
            // Vérification 4: Configuration RTL
            echo "<div class='test-section'>";
            echo "<h2>✅ Test 4: Configuration RTL/LTR</h2>";
            
            $indexContent = file_get_contents('index.php');
            $cssContent = file_get_contents('styles.css');
            $jsContent = file_get_contents('script.js');
            
            $checks = [
                'HTML dir="rtl"' => strpos($indexContent, 'dir="rtl"') !== false,
                'CSS [dir="rtl"]' => strpos($cssContent, '[dir="rtl"]') !== false,
                'CSS [dir="ltr"]' => strpos($cssContent, '[dir="ltr"]') !== false,
                'JS direction handling' => strpos($jsContent, 'document.documentElement.dir') !== false,
                'Language switcher AR' => strpos($indexContent, 'data-lang="ar"') !== false
            ];
            
            foreach ($checks as $check => $result) {
                $status = $result ? "✅ OK" : "❌ Manquant";
                $class = $result ? "success" : "error";
                echo "<div class='status $class'>$status $check</div>";
                if (!$result) $errors[] = $check;
            }
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div class='status error'>❌ Erreur: " . $e->getMessage() . "</div>";
            $errors[] = $e->getMessage();
        }
        
        // Résumé final
        echo "<div class='test-section'>";
        echo "<h2>📊 Résumé Final</h2>";
        if (empty($errors)) {
            echo "<div class='status success'>";
            echo "<h3>🎉 SUCCÈS COMPLET!</h3>";
            echo "<p>✅ Toutes les vérifications sont passées avec succès</p>";
            echo "<p>✅ La bidirectionnalité Arabe/Anglais est correctement implémentée</p>";
            echo "<p>✅ Le site peut basculer entre RTL (Arabe) et LTR (Anglais)</p>";
            echo "</div>";
        } else {
            echo "<div class='status error'>";
            echo "<h3>⚠️ PROBLÈMES DÉTECTÉS</h3>";
            echo "<ul>";
            foreach ($errors as $error) {
                echo "<li>$error</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        echo "</div>";
        
        // Instructions de test
        echo "<div class='test-section'>";
        echo "<h2>🧪 Instructions de Test Manuel</h2>";
        echo "<div class='status info'>";
        echo "<ol>";
        echo "<li>Ouvrez <a href='index.php' target='_blank'>index.php</a> dans votre navigateur</li>";
        echo "<li>Vérifiez que le site s'affiche en arabe par défaut (RTL)</li>";
        echo "<li>Cliquez sur le bouton 'EN' pour basculer en anglais (LTR)</li>";
        echo "<li>Cliquez sur le bouton 'AR' pour revenir en arabe (RTL)</li>";
        echo "<li>Vérifiez que tous les textes changent correctement</li>";
        echo "<li>Testez sur mobile pour vérifier la responsivité</li>";
        echo "</ol>";
        echo "</div>";
        echo "</div>";
        ?>
        
        <div class="test-section">
            <h2>🔗 Liens de Test</h2>
            <p><a href="index.php" target="_blank">🌐 Site Principal</a></p>
            <p><a href="test_bidirectional.html" target="_blank">🧪 Test Bidirectionnel</a></p>
            <p><a href="verify_arabic.php" target="_blank">📊 Vérification Base de Données</a></p>
        </div>
    </div>
</body>
</html>
