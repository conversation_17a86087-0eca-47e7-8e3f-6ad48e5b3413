# Documentation API Backend - Madjour Industries

## Vue d'ensemble

Cette documentation décrit tous les endpoints et fonctionnalités du backend de l'application Madjour Industries. Le backend est développé en PHP avec une base de données MySQL et utilise une architecture MVC simplifiée.

## Configuration de Base

### Base de Données
- **Host**: localhost
- **Port**: 3306
- **Database**: madjour_industries
- **Charset**: utf8mb4
- **Collation**: utf8mb4_unicode_ci

### Structure des Tables

#### Table `categories`
```sql
CREATE TABLE categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name_en VARCHAR(255) NOT NULL,
    name_ar VARCHAR(255) NOT NULL
);
```

#### Table `products`
```sql
CREATE TABLE products (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    category_id INT,
    description_en TEXT,
    description_ar TEXT,
    image_path VARCHAR(500),
    FOREIGN KEY (category_id) REFERENCES categories(id)
);
```

#### Table `admins`
```sql
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL
);
```

## Endpoints Publics

### 1. Page d'Accueil
**Endpoint**: `GET /index.php`
**Description**: Page principale du site avec affichage des produits et catégories
**Paramètres**: Aucun
**Réponse**: Page HTML complète

**Fonctionnalités**:
- Récupération de toutes les catégories
- Récupération de tous les produits avec leurs catégories
- Support multilingue (EN/AR)
- Interface responsive

**Requêtes SQL exécutées**:
```sql
SELECT id, name_en, name_ar FROM categories ORDER BY id
SELECT p.id, p.name, p.category_id, p.description_en, p.description_ar, p.image_path, c.name_en AS category_name_en
FROM products p
JOIN categories c ON p.category_id = c.id
ORDER BY p.category_id, p.id
```

### 2. API de Traductions
**Endpoint**: `GET /translations.php`
**Description**: Fournit les traductions dynamiques en format JavaScript
**Paramètres**: Aucun
**Content-Type**: `text/javascript`

**Fonctionnalités**:
- Cache des traductions (TTL: 1 heure)
- Génération automatique des traductions depuis la base de données
- Support pour les catégories et produits

**Structure de réponse**:
```javascript
const translations = {
    "en": {
        "categories.category_name": "Category Name",
        "products.product_name": "Product Description"
    },
    "ar": {
        "categories.category_name": "اسم الفئة",
        "products.product_name": "وصف المنتج"
    }
};
```

## Endpoints d'Administration

### 3. Connexion Admin
**Endpoint**: `POST /admin_login.php`
**Description**: Authentification des administrateurs
**Méthode**: POST

**Paramètres**:
- `username` (string, required): Nom d'utilisateur
- `password` (string, required): Mot de passe

**Sécurité**:
- Protection CSRF
- Hachage des mots de passe avec `password_hash()`
- Vérification avec `password_verify()`
- Régénération de l'ID de session

**Réponse**:
- **Succès**: Redirection vers `/admin.php`
- **Échec**: Message d'erreur affiché

### 4. Panel d'Administration
**Endpoint**: `GET /admin.php`
**Description**: Interface d'administration pour gérer les produits et catégories
**Authentification**: Requise (session admin)

**Fonctionnalités**:
- Gestion des produits (CRUD)
- Gestion des catégories (CRUD)
- Upload d'images
- Protection CSRF

## Endpoints CRUD - Produits

### 5. Ajouter un Produit
**Endpoint**: `POST /admin.php`
**Action**: `add_product`
**Authentification**: Requise

**Paramètres**:
- `product_name` (string, required): Nom du produit
- `category_id` (int, required): ID de la catégorie
- `description_en` (text): Description en anglais
- `description_ar` (text): Description en arabe
- `product_image` (file): Image du produit
- `csrf_token` (string, required): Token CSRF

**Validation**:
- Vérification de l'existence de la catégorie
- Validation du format d'image
- Filtrage des entrées avec `FILTER_UNSAFE_RAW`

**Requête SQL**:
```sql
INSERT INTO products (name, category_id, description_en, description_ar, image_path)
VALUES (:name, :category_id, :description_en, :description_ar, :image_path)
```

### 6. Modifier un Produit
**Endpoint**: `POST /admin.php`
**Action**: `update_product`
**Authentification**: Requise

**Paramètres**:
- `product_id` (int, required): ID du produit à modifier
- `product_name` (string, required): Nouveau nom
- `category_id` (int, required): Nouvelle catégorie
- `description_en` (text): Nouvelle description EN
- `description_ar` (text): Nouvelle description AR
- `product_image` (file, optional): Nouvelle image
- `csrf_token` (string, required): Token CSRF

**Requête SQL**:
```sql
UPDATE products SET name = :name, category_id = :category_id, 
description_en = :description_en, description_ar = :description_ar, 
image_path = :image_path WHERE id = :id
```

### 7. Supprimer un Produit
**Endpoint**: `POST /admin.php`
**Action**: `delete_product`
**Authentification**: Requise

**Paramètres**:
- `product_id` (int, required): ID du produit à supprimer
- `csrf_token` (string, required): Token CSRF

**Fonctionnalités**:
- Suppression du fichier image associé
- Vérification de l'existence du produit

**Requête SQL**:
```sql
DELETE FROM products WHERE id = :id
```

## Endpoints CRUD - Catégories

### 8. Ajouter une Catégorie
**Endpoint**: `POST /admin.php`
**Action**: `add_category`
**Authentification**: Requise

**Paramètres**:
- `category_name_en` (string, required): Nom en anglais
- `category_name_ar` (string, required): Nom en arabe
- `csrf_token` (string, required): Token CSRF

**Validation**:
- Vérification de l'unicité du nom
- Support UTF-8 complet

**Requête SQL**:
```sql
INSERT INTO categories (name_en, name_ar) VALUES (:name_en, :name_ar)
```

### 9. Modifier une Catégorie
**Endpoint**: `POST /admin.php`
**Action**: `update_category`
**Authentification**: Requise

**Paramètres**:
- `category_id` (int, required): ID de la catégorie
- `category_name_en` (string, required): Nouveau nom EN
- `category_name_ar` (string, required): Nouveau nom AR
- `csrf_token` (string, required): Token CSRF

**Requête SQL**:
```sql
UPDATE categories SET name_en = :name_en, name_ar = :name_ar WHERE id = :id
```

## Gestion des Fichiers

### 10. Upload d'Images
**Fonction**: `handleImageUpload()`
**Répertoire**: `/Uploads/`
**Formats supportés**: JPG, JPEG, PNG, GIF, WEBP
**Taille maximale**: Définie par PHP
**Sécurité**: Validation du type MIME

**Fonctionnalités**:
- Génération de noms uniques avec timestamp
- Création automatique du répertoire
- Gestion des erreurs d'upload
- Suppression des anciennes images lors de la mise à jour

## Sécurité

### Protection CSRF
- Génération de tokens uniques par session
- Validation obligatoire pour toutes les actions POST
- Régénération automatique des tokens

### Authentification
- Sessions PHP sécurisées
- Hachage des mots de passe avec `password_hash()`
- Vérification avec `password_verify()`
- Timeout de session

### Validation des Données
- Filtrage avec `filter_input()`
- Utilisation de `FILTER_UNSAFE_RAW` pour UTF-8
- Requêtes préparées (PDO)
- Échappement HTML avec `htmlspecialchars()`

## Gestion des Erreurs

### Logging
- Fichier de log: `debug.log`
- Horodatage automatique
- Détails des erreurs de connexion
- Suivi des tentatives de connexion

### Gestion des Exceptions
- Try-catch pour toutes les opérations de base de données
- Messages d'erreur utilisateur-friendly
- Logging détaillé pour le débogage

## Configuration et Optimisation

### Base de Données
- Singleton pattern pour la connexion
- Pool de connexions avec retry automatique
- Configuration UTF-8 complète
- Timeout de connexion configuré

### Cache
- Cache des traductions (1 heure)
- Fichiers JSON pour les performances
- Invalidation automatique

### Performance
- Requêtes optimisées avec JOIN
- Index sur les clés étrangères
- Pagination possible (non implémentée)

## Codes de Statut et Réponses

### Succès
- **200 OK**: Opération réussie
- **302 Found**: Redirection après action

### Erreurs
- **400 Bad Request**: Paramètres invalides
- **401 Unauthorized**: Non authentifié
- **403 Forbidden**: Token CSRF invalide
- **404 Not Found**: Ressource introuvable
- **500 Internal Server Error**: Erreur serveur

## Exemples d'Utilisation

### Connexion Admin
```bash
curl -X POST http://localhost/admin_login.php \
  -d "username=admin&password=admin123"
```

### Ajouter un Produit
```bash
curl -X POST http://localhost/admin.php \
  -F "add_product=1" \
  -F "product_name=Nouveau Produit" \
  -F "category_id=1" \
  -F "description_en=English description" \
  -F "description_ar=وصف بالعربية" \
  -F "product_image=@image.jpg" \
  -F "csrf_token=TOKEN_VALUE"
```

## Scripts de Maintenance

### Scripts Disponibles
- `run_all_fixes.php`: Correction automatique des données
- `fix_database_data.php`: Correction des données incohérentes
- `fix_security.php`: Amélioration de la sécurité
- `fix_translations.php`: Vérification des traductions
- `test_caracteres_speciaux.php`: Test des caractères UTF-8

## Informations de Déploiement

### Prérequis
- PHP 7.4+
- MySQL 5.7+
- Extension PDO MySQL
- Support UTF-8 complet

### Configuration Recommandée
- `upload_max_filesize`: 10M
- `post_max_size`: 10M
- `memory_limit`: 128M
- `max_execution_time`: 30

---

**Version**: 1.0  
**Dernière mise à jour**: 2025-01-27  
**Auteur**: Documentation générée automatiquement
