<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Language Buttons Visibility</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="styles.css?v=1.2">
    <style>
        .test-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: #fff;
            border: 2px solid #D32F2F;
            padding: 1rem;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 10000;
            max-width: 300px;
        }
        
        .test-info h3 {
            color: #D32F2F;
            margin: 0 0 0.5rem 0;
            font-size: 1rem;
        }
        
        .test-info ul {
            margin: 0;
            padding-left: 1.5rem;
            font-size: 0.9rem;
        }
        
        .test-info li {
            margin: 0.25rem 0;
        }
        
        .status {
            font-weight: bold;
        }
        
        .status.correct {
            color: green;
        }
        
        .status.incorrect {
            color: red;
        }
        
        .screen-size {
            background: #f0f0f0;
            padding: 0.5rem;
            border-radius: 4px;
            margin: 0.5rem 0;
            text-align: center;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-top">
            <div class="header-content">
                <div class="logo">
                    <img src="img/logo/logo madjour.png" alt="MADJOUR Logo" width="60" height="60" loading="lazy">
                </div>
                <div class="header-right">
                    <div class="language-switch">
                        <button class="lang-btn" data-lang="en">EN</button>
                        <button class="lang-btn active" data-lang="ar">AR</button>
                    </div>
                    <div class="search-container">
                        <input type="text" class="search-bar" placeholder="البحث عن المنتجات...">
                        <button class="search-btn"><span class="material-icons">search</span></button>
                    </div>
                </div>
            </div>
        </div>
        <nav class="nav">
            <div class="nav-container">
                <button class="mobile-menu-btn" aria-label="Toggle Menu">☰</button>
                <ul class="nav-menu">
                    <!-- Language switcher for mobile menu -->
                    <li class="mobile-language-switch">
                        <button class="lang-btn" data-lang="en">EN</button>
                        <button class="lang-btn active" data-lang="ar">AR</button>
                    </li>
                    <li class="nav-item"><a href="#home" class="nav-link active">الرئيسية</a></li>
                    <li class="nav-item"><a href="#categories" class="nav-link">الفئات</a></li>
                    <li class="nav-item"><a href="#products" class="nav-link">المنتجات</a></li>
                    <li class="nav-item"><a href="#about" class="nav-link">من نحن</a></li>
                    <li class="nav-item"><a href="#contact" class="nav-link">اتصل بنا</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <main style="padding: 2rem; background: #f5f5f5; min-height: 100vh;">
        <h1 style="color: #D32F2F; text-align: center;">اختبار عرض أزرار اللغة</h1>
        <p style="text-align: center; color: #666;">قم بتغيير حجم النافذة لاختبار السلوك المتجاوب</p>
        
        <div style="background: white; padding: 2rem; border-radius: 8px; margin: 2rem 0;">
            <h2>التوقعات:</h2>
            <ul>
                <li><strong>الشاشات الكبيرة (>768px):</strong> أزرار اللغة في الرأس فقط، غير مرئية في شريط التنقل</li>
                <li><strong>الشاشات الصغيرة (≤768px):</strong> أزرار اللغة في القائمة المنسدلة فقط</li>
            </ul>
        </div>
    </main>

    <div class="test-info">
        <h3>🧪 حالة الاختبار</h3>
        <div class="screen-size" id="screen-size"></div>
        <ul>
            <li>أزرار اللغة في الرأس: <span class="status" id="header-status"></span></li>
            <li>أزرار اللغة في التنقل: <span class="status" id="nav-status"></span></li>
            <li>زر القائمة المحمول: <span class="status" id="mobile-btn-status"></span></li>
        </ul>
    </div>

    <script src="script.js"></script>
    <script>
        function checkLanguageButtonsVisibility() {
            const screenWidth = window.innerWidth;
            const headerLangSwitch = document.querySelector('.header-top .language-switch');
            const navLangSwitch = document.querySelector('.nav-menu .mobile-language-switch');
            const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
            
            // Update screen size display
            document.getElementById('screen-size').textContent = `${screenWidth}px`;
            
            // Check header language buttons
            const headerVisible = headerLangSwitch && window.getComputedStyle(headerLangSwitch).display !== 'none';
            const headerStatus = document.getElementById('header-status');
            
            // Check nav language buttons
            const navVisible = navLangSwitch && window.getComputedStyle(navLangSwitch).display !== 'none';
            const navStatus = document.getElementById('nav-status');
            
            // Check mobile menu button
            const mobileVisible = mobileMenuBtn && window.getComputedStyle(mobileMenuBtn).display !== 'none';
            const mobileStatus = document.getElementById('mobile-btn-status');
            
            if (screenWidth > 768) {
                // Desktop: header buttons should be visible, nav buttons should be hidden
                headerStatus.textContent = headerVisible ? '✅ مرئية' : '❌ مخفية';
                headerStatus.className = `status ${headerVisible ? 'correct' : 'incorrect'}`;
                
                navStatus.textContent = navVisible ? '❌ مرئية (خطأ!)' : '✅ مخفية';
                navStatus.className = `status ${navVisible ? 'incorrect' : 'correct'}`;
                
                mobileStatus.textContent = mobileVisible ? '❌ مرئي (خطأ!)' : '✅ مخفي';
                mobileStatus.className = `status ${mobileVisible ? 'incorrect' : 'correct'}`;
            } else {
                // Mobile: header buttons should be hidden, nav buttons should be visible when menu is open
                headerStatus.textContent = headerVisible ? '❌ مرئية (خطأ!)' : '✅ مخفية';
                headerStatus.className = `status ${headerVisible ? 'incorrect' : 'correct'}`;
                
                navStatus.textContent = '✅ في القائمة';
                navStatus.className = 'status correct';
                
                mobileStatus.textContent = mobileVisible ? '✅ مرئي' : '❌ مخفي';
                mobileStatus.className = `status ${mobileVisible ? 'correct' : 'incorrect'}`;
            }
        }
        
        // Check on load and resize
        window.addEventListener('load', checkLanguageButtonsVisibility);
        window.addEventListener('resize', checkLanguageButtonsVisibility);
        
        // Initial check
        checkLanguageButtonsVisibility();
    </script>
</body>
</html>
