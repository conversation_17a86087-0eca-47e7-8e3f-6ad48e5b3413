<?php
// migrate_to_arabic.php - Script to migrate database from French to Arabic columns
require_once 'db_connect.php';

try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "Starting migration from French to Arabic...\n";
    
    // Step 1: Add new Arabic columns to categories table
    echo "1. Adding name_ar column to categories table...\n";
    $conn->exec("ALTER TABLE categories ADD COLUMN name_ar VARCHAR(255) NOT NULL DEFAULT ''");
    
    // Step 2: Add new Arabic columns to products table
    echo "2. Adding description_ar column to products table...\n";
    $conn->exec("ALTER TABLE products ADD COLUMN description_ar TEXT NOT NULL DEFAULT ''");
    
    // Step 3: Update categories with Arabic translations
    echo "3. Updating categories with Arabic translations...\n";
    $categoryTranslations = [
        'Lighters & Gas' => 'الولاعات والغاز',
        'Toothbrushes' => 'فرش الأسنان',
        'Adhesive Glue' => 'الغراء اللاصق',
        'Baby Products' => 'منتجات الأطفال',
        'Insecticides' => 'المبيدات الحشرية'
    ];
    
    foreach ($categoryTranslations as $english => $arabic) {
        $stmt = $conn->prepare("UPDATE categories SET name_ar = :arabic WHERE name_en = :english");
        $stmt->execute([':arabic' => $arabic, ':english' => $english]);
        echo "   Updated: $english -> $arabic\n";
    }
    
    // Step 4: Update products with Arabic descriptions
    echo "4. Updating products with Arabic descriptions...\n";
    $productTranslations = [
        'baby product' => 'منتج للأطفال',
        'Lighters' => 'ولاعات',
        'produit des bébé' => 'منتج للأطفال',
        'produit de bébé' => 'منتج للأطفال',
        'Briquets' => 'ولاعات'
    ];
    
    // Update products based on their current French descriptions
    $products = $conn->query("SELECT id, description_fr FROM products")->fetchAll();
    foreach ($products as $product) {
        $arabicDesc = $productTranslations[$product['description_fr']] ?? 'منتج';
        $stmt = $conn->prepare("UPDATE products SET description_ar = :arabic WHERE id = :id");
        $stmt->execute([':arabic' => $arabicDesc, ':id' => $product['id']]);
        echo "   Updated product ID {$product['id']}: {$product['description_fr']} -> $arabicDesc\n";
    }
    
    // Step 5: Add indexes for new Arabic columns
    echo "5. Adding indexes for Arabic columns...\n";
    $conn->exec("ALTER TABLE categories ADD INDEX idx_name_ar (name_ar)");
    
    // Step 6: Drop old French columns (optional - commented out for safety)
    echo "6. French columns kept for backup (uncomment to remove)...\n";
    // $conn->exec("ALTER TABLE categories DROP COLUMN name_fr");
    // $conn->exec("ALTER TABLE products DROP COLUMN description_fr");
    
    echo "\nMigration completed successfully!\n";
    echo "Arabic columns have been added and populated.\n";
    echo "French columns are still present for backup purposes.\n";
    
} catch (Exception $e) {
    echo "Migration failed: " . $e->getMessage() . "\n";
    echo "Please check your database connection and try again.\n";
}
?>
