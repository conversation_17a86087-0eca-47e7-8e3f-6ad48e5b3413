<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Bidirectional - Madjour Industries</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 5px;
        }
        .lang-switcher {
            margin-bottom: 20px;
        }
        .lang-btn {
            padding: 10px 20px;
            margin: 5px;
            border: 2px solid #D32F2F;
            background: white;
            color: #D32F2F;
            cursor: pointer;
            border-radius: 5px;
        }
        .lang-btn.active {
            background: #D32F2F;
            color: white;
        }
        [dir="rtl"] {
            text-align: right;
        }
        [dir="ltr"] {
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="lang-switcher">
        <button class="lang-btn active" data-lang="ar">العربية</button>
        <button class="lang-btn" data-lang="en">English</button>
    </div>

    <div class="test-section">
        <h2 data-i18n="nav.home">الرئيسية</h2>
        <p data-i18n="hero.title">منتجات موثوقة للحياة اليومية</p>
        <p data-i18n="hero.subtitle">صناعات مجور - جودة يمكنك الاعتماد عليها</p>
    </div>

    <div class="test-section">
        <h3 data-i18n="categories.title">الفئات</h3>
        <ul>
            <li data-i18n="categories.lighters_gas">الولاعات والغاز</li>
            <li data-i18n="categories.toothbrushes">فرش الأسنان</li>
            <li data-i18n="categories.glue">الغراء اللاصق</li>
            <li data-i18n="categories.baby">منتجات الأطفال</li>
            <li data-i18n="categories.insecticides">المبيدات الحشرية</li>
        </ul>
    </div>

    <div class="test-section">
        <h3 data-i18n="about.title">حول شركة EURL MADJOUR HICHEM</h3>
        <p data-i18n="about.description">شركة EURL MADJOUR HICHEM هي شركة رائدة في عدة قطاعات، تضم عدة وحدات تصنيع، بما في ذلك الولاعات والغاز والغراء والمبيدات الحشرية والمواد شبه الصيدلانية.</p>
    </div>

    <script>
        // Simplified translation object for testing
        const translations = {
            en: {
                'nav.home': 'Home',
                'hero.title': 'Trusted Products for Everyday Life',
                'hero.subtitle': 'Madjour Industries Quality You Can Count On',
                'categories.title': 'Categories',
                'categories.lighters_gas': 'Lighters & Gas',
                'categories.toothbrushes': 'Toothbrushes',
                'categories.glue': 'Adhesive Glue',
                'categories.baby': 'Baby Products',
                'categories.insecticides': 'Insecticides',
                'about.title': 'About EURL MADJOUR HICHEM',
                'about.description': 'EURL MADJOUR HICHEM is a leading company in multiple sectors, comprising several manufacturing units, including lighters, gas, glue, insecticides, and semi-pharmaceutical materials.'
            },
            ar: {
                'nav.home': 'الرئيسية',
                'hero.title': 'منتجات موثوقة للحياة اليومية',
                'hero.subtitle': 'صناعات مجور - جودة يمكنك الاعتماد عليها',
                'categories.title': 'الفئات',
                'categories.lighters_gas': 'الولاعات والغاز',
                'categories.toothbrushes': 'فرش الأسنان',
                'categories.glue': 'الغراء اللاصق',
                'categories.baby': 'منتجات الأطفال',
                'categories.insecticides': 'المبيدات الحشرية',
                'about.title': 'حول شركة EURL MADJOUR HICHEM',
                'about.description': 'شركة EURL MADJOUR HICHEM هي شركة رائدة في عدة قطاعات، تضم عدة وحدات تصنيع، بما في ذلك الولاعات والغاز والغراء والمبيدات الحشرية والمواد شبه الصيدلانية.'
            }
        };

        let currentLang = 'ar';

        function updateLanguage(lang) {
            document.querySelectorAll('[data-i18n]').forEach(element => {
                const key = element.dataset.i18n;
                element.textContent = translations[lang][key] || element.textContent;
            });

            // Update document language and direction
            document.documentElement.lang = lang;
            document.documentElement.dir = lang === 'ar' ? 'rtl' : 'ltr';
            
            // Update body direction for proper layout
            document.body.style.direction = lang === 'ar' ? 'rtl' : 'ltr';
            document.body.style.textAlign = lang === 'ar' ? 'right' : 'left';
        }

        function handleLanguageSwitch(e) {
            const newLang = e.target.dataset.lang;
            if (!newLang || newLang === currentLang) return;

            currentLang = newLang;
            updateLanguage(newLang);
            
            // Update active button
            document.querySelectorAll('.lang-btn').forEach(btn => {
                btn.classList.toggle('active', btn === e.target);
            });
        }

        // Initialize
        document.querySelectorAll('.lang-btn').forEach(btn => {
            btn.addEventListener('click', handleLanguageSwitch);
        });

        updateLanguage(currentLang);
    </script>
</body>
</html>
