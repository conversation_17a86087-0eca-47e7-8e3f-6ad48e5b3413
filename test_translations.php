<?php
// test_translations.php - Test des traductions des catégories
require_once 'db_connect.php';

try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "=== TEST DES TRADUCTIONS DES CATÉGORIES ===\n\n";
    
    // Récupérer les catégories
    $categories = $conn->query("SELECT id, name_en, name_ar FROM categories ORDER BY id")->fetchAll();
    
    echo "Catégories dans la base de données:\n";
    echo "====================================\n";
    foreach ($categories as $category) {
        echo "ID: {$category['id']}\n";
        echo "  EN: {$category['name_en']}\n";
        echo "  AR: {$category['name_ar']}\n";
        
        // Générer la clé de traduction comme dans index.php
        $translation_key = strtolower(str_replace([' & ', ' '], ['_', '_'], $category['name_en']));
        echo "  Clé: categories.{$translation_key}\n";
        echo "  ---\n";
    }
    
    echo "\nClés de traduction attendues dans script.js:\n";
    echo "============================================\n";
    
    $expected_translations = [
        'categories.lighters_gas' => [
            'en' => 'Lighters & Gas',
            'ar' => 'الولاعات والغاز'
        ],
        'categories.toothbrushes' => [
            'en' => 'Toothbrushes', 
            'ar' => 'فرش الأسنان'
        ],
        'categories.adhesive_glue' => [
            'en' => 'Adhesive Glue',
            'ar' => 'الغراء اللاصق'
        ],
        'categories.baby_products' => [
            'en' => 'Baby Products',
            'ar' => 'منتجات الأطفال'
        ],
        'categories.insecticides' => [
            'en' => 'Insecticides',
            'ar' => 'المبيدات الحشرية'
        ]
    ];
    
    foreach ($expected_translations as $key => $translations) {
        echo "'{$key}': {\n";
        echo "  EN: '{$translations['en']}'\n";
        echo "  AR: '{$translations['ar']}'\n";
        echo "}\n";
    }
    
    echo "\n=== VÉRIFICATION DE LA CORRESPONDANCE ===\n";
    
    $all_match = true;
    foreach ($categories as $category) {
        $generated_key = 'categories.' . strtolower(str_replace([' & ', ' '], ['_', '_'], $category['name_en']));
        
        if (isset($expected_translations[$generated_key])) {
            $expected_en = $expected_translations[$generated_key]['en'];
            $expected_ar = $expected_translations[$generated_key]['ar'];
            
            $en_match = $category['name_en'] === $expected_en;
            $ar_match = $category['name_ar'] === $expected_ar;
            
            echo "✓ {$generated_key}:\n";
            echo "  EN: " . ($en_match ? "✓" : "✗") . " '{$category['name_en']}'\n";
            echo "  AR: " . ($ar_match ? "✓" : "✗") . " '{$category['name_ar']}'\n";
            
            if (!$en_match || !$ar_match) {
                $all_match = false;
            }
        } else {
            echo "✗ Clé manquante: {$generated_key}\n";
            $all_match = false;
        }
    }
    
    echo "\n=== RÉSULTAT ===\n";
    if ($all_match) {
        echo "✅ TOUTES LES TRADUCTIONS CORRESPONDENT!\n";
        echo "Les noms de catégories devraient maintenant s'afficher correctement en arabe.\n";
    } else {
        echo "❌ CERTAINES TRADUCTIONS NE CORRESPONDENT PAS!\n";
        echo "Vérifiez les clés dans script.js.\n";
    }
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
}
?>
