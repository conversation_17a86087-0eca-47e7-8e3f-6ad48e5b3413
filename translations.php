<?php
// translations.php
require_once 'db_connect.php';

// Basic file-based caching
$cache_file = 'cache/translations.json';
$cache_ttl = 3600; // 1 hour

$translations = [];
if (file_exists($cache_file) && (time() - filemtime($cache_file)) < $cache_ttl) {
    $translations = json_decode(file_get_contents($cache_file), true);
} else {
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        $products = $conn->query("SELECT id, name, description_en, description_ar FROM products")->fetchAll();
        $categories = $conn->query("SELECT name_en, name_ar FROM categories")->fetchAll();

        $translations = [
    'en' => [
        'title' => 'Trusted Products',
        'home' => 'Home',
        'categories' => 'Categories',
        'products' => 'Products',
        'about' => 'About Us',
        'contact' => 'Contact',
        'admin' => 'Admin',
        'welcome' => 'Trusted Products for Everyday Life',
        'tagline' => 'Madjour Industries Quality You Can Count On',
        'view_details' => 'View Details',
        'category' => 'Category',
        'all_categories' => 'All Categories',
        'categories.lighters_&_gas' => 'Lighters & Gas',
        'categories.toothbrushes' => 'Toothbrushes',
        'categories.adhesive_glue' => 'Adhesive Glue',
        'categories.baby_products' => 'Baby Products',
        'categories.insecticides' => 'Insecticides'
    ],
    'ar' => [
        'title' => 'منتجات موثوقة',
        'home' => 'الرئيسية',
        'categories' => 'الفئات',
        'products' => 'منتجاتنا',
        'about' => 'من نحن',
        'contact' => 'اتصل بنا',
        'admin' => 'الإدارة',
        'welcome' => 'منتجات موثوقة للحياة اليومية',
        'tagline' => 'جودة صناعات مجور التي يمكنك الاعتماد عليها',
        'view_details' => 'عرض التفاصيل',
        'category' => 'الفئة',
        'all_categories' => 'جميع الفئات',
        'categories.lighters_&_gas' => 'الولاعات والغاز',
        'categories.toothbrushes' => 'فرش الأسنان',
        'categories.adhesive_glue' => 'الغراء اللاصق',
        'categories.baby_products' => 'منتجات الأطفال',
        'categories.insecticides' => 'المبيدات الحشرية'
    ]
];
        foreach ($products as $product) {
            $product_id = strtolower(str_replace(' ', '_', $product['name']));
            $translations['en']['products.' . $product_id] = $product['name'];
            $translations['ar']['products.' . $product_id] = $product['name'];
            $translations['en']['products.' . $product_id . '_desc'] = $product['description_en'];
            $translations['ar']['products.' . $product_id . '_desc'] = $product['description_ar'];
        }

        foreach ($categories as $category) {
            $category_slug = strtolower(str_replace(' ', '_', $category['name_en']));
            $translations['en']['categories.' . $category_slug] = $category['name_en'];
            $translations['ar']['categories.' . $category_slug] = $category['name_ar'];
        }

        // Save to cache
        if (!is_dir('cache')) {
            mkdir('cache', 0755, true);
        }
        file_put_contents($cache_file, json_encode($translations));
    } catch (Exception $e) {
        file_put_contents('debug.log', "translations.php: {$e->getMessage()}\n", FILE_APPEND);
        $translations = []; // Fallback to empty translations
    }
}

// Set content type to JavaScript
header('Content-Type: text/javascript');

// Output JavaScript
echo "const translations = " . json_encode($translations) . ";";
?>