<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Modal Title Centering</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="styles.css?v=1.3">
    <style>
        .test-container {
            padding: 2rem;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .test-section {
            background: white;
            padding: 2rem;
            margin: 2rem 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-products {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .test-product {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-product img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 4px;
            margin-bottom: 0.5rem;
        }
        
        .test-product h4 {
            color: var(--dark-gray);
            margin: 0.5rem 0;
            font-size: 1rem;
            line-height: 1.3;
        }
        
        .language-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-red);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            z-index: 10000;
            font-weight: bold;
        }
        
        .test-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
        }
        
        .test-info h3 {
            color: #1976d2;
            margin: 0 0 0.5rem 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-section">
            <h1 style="color: var(--primary-red); text-align: center;">اختبار توسيط عناوين المنتجات في النوافذ المنبثقة</h1>
            
            <div class="test-info">
                <h3>📋 ما يتم اختباره:</h3>
                <ul>
                    <li>توسيط اسم المنتج في النافذة المنبثقة</li>
                    <li>التفاف النص الطويل بشكل صحيح</li>
                    <li>عمل التوسيط في كلا الاتجاهين (RTL/LTR)</li>
                    <li>الاستجابة على أحجام الشاشات المختلفة</li>
                </ul>
            </div>
            
            <div class="test-products">
                <div class="test-product">
                    <img src="img/logo/logo madjour.png" alt="منتج قصير">
                    <h4>منتج قصير</h4>
                    <button class="view-details-btn" 
                            data-product-name="منتج قصير"
                            data-description-ar="وصف بسيط لمنتج قصير الاسم."
                            data-description-en="Simple description for a short-named product.">
                        عرض التفاصيل
                    </button>
                </div>
                
                <div class="test-product">
                    <img src="img/logo/logo madjour.png" alt="منتج طويل">
                    <h4>منتج بعنوان طويل جداً يحتاج إلى التفاف</h4>
                    <button class="view-details-btn" 
                            data-product-name="منتج بعنوان طويل جداً يحتاج إلى التفاف النص بشكل صحيح"
                            data-description-ar="هذا منتج له اسم طويل جداً لاختبار كيفية التعامل مع النصوص الطويلة في النوافذ المنبثقة."
                            data-description-en="This is a product with a very long name to test how long texts are handled in modal windows.">
                        عرض التفاصيل
                    </button>
                </div>
                
                <div class="test-product">
                    <img src="img/logo/logo madjour.png" alt="منتج متوسط">
                    <h4>منتج بعنوان متوسط الطول</h4>
                    <button class="view-details-btn" 
                            data-product-name="منتج بعنوان متوسط الطول للاختبار"
                            data-description-ar="منتج بعنوان متوسط الطول لاختبار التوسيط."
                            data-description-en="Product with medium-length title for centering test.">
                        عرض التفاصيل
                    </button>
                </div>
                
                <div class="test-product">
                    <img src="img/logo/logo madjour.png" alt="English Product">
                    <h4>English Product Name</h4>
                    <button class="view-details-btn" 
                            data-product-name="English Product with Very Long Name That Should Wrap Properly"
                            data-description-ar="منتج إنجليزي لاختبار التوسيط."
                            data-description-en="English product for testing centering behavior with LTR text direction.">
                        View Details
                    </button>
                </div>
            </div>
            
            <div class="test-info">
                <h3>🎯 التوقعات:</h3>
                <ul>
                    <li><strong>العنوان:</strong> يجب أن يكون في المنتصف تماماً</li>
                    <li><strong>النص الطويل:</strong> يجب أن يلتف بشكل صحيح مع بقاء التوسيط</li>
                    <li><strong>الاتجاه:</strong> التوسيط يعمل في RTL و LTR</li>
                    <li><strong>الاستجابة:</strong> التوسيط محفوظ على جميع أحجام الشاشات</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Product Modal -->
    <div class="product-modal" id="product-modal">
        <div class="modal-content">
            <button class="modal-close-btn" aria-label="Close Modal">×</button>
            <h3 class="modal-title"></h3>
            <div class="modal-image">
                <img class="modal-product-image" src="" alt="">
            </div>
            <p class="modal-description"></p>
            <button class="modal-close-btn secondary">إغلاق</button>
        </div>
    </div>

    <button class="language-toggle" onclick="toggleLanguage()">
        تبديل اللغة / Toggle Language
    </button>

    <script src="script.js"></script>
    <script>
        // Language toggle functionality
        function toggleLanguage() {
            const html = document.documentElement;
            const currentLang = html.getAttribute('lang');
            const currentDir = html.getAttribute('dir');
            
            if (currentLang === 'ar') {
                html.setAttribute('lang', 'en');
                html.setAttribute('dir', 'ltr');
                document.querySelector('.modal-close-btn.secondary').textContent = 'Close';
            } else {
                html.setAttribute('lang', 'ar');
                html.setAttribute('dir', 'rtl');
                document.querySelector('.modal-close-btn.secondary').textContent = 'إغلاق';
            }
        }
        
        // Modal functionality
        document.querySelectorAll('.view-details-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const modal = document.getElementById('product-modal');
                const title = document.querySelector('.modal-title');
                const image = document.querySelector('.modal-product-image');
                const description = document.querySelector('.modal-description');
                
                const productName = this.dataset.productName;
                const currentLang = document.documentElement.getAttribute('lang');
                const descriptionText = currentLang === 'ar' ? 
                    this.dataset.descriptionAr : 
                    this.dataset.descriptionEn;
                
                title.textContent = productName;
                image.src = 'img/logo/logo madjour.png';
                image.alt = productName;
                description.textContent = descriptionText;
                
                modal.classList.add('active');
            });
        });
        
        // Close modal functionality
        document.querySelectorAll('.modal-close-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.getElementById('product-modal').classList.remove('active');
            });
        });
        
        // Close modal on background click
        document.getElementById('product-modal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.remove('active');
            }
        });
    </script>
</body>
</html>
