<?php
// fix_database_data.php
require_once 'db_connect.php';

try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    echo "Début de la correction des données de la base de données...\n";
    
    // Corriger les descriptions incohérentes dans les produits
    $corrections = [
        // ID 15: Correction de "Briquets" vers "ولاعات" en arabe
        15 => ['description_ar' => 'ولاعات'],
        16 => ['description_ar' => 'ولاعات'],
        17 => ['description_ar' => 'ولاعات'],
        18 => ['description_ar' => 'ولاعات']
    ];
    
    foreach ($corrections as $product_id => $updates) {
        $sql = "UPDATE products SET description_ar = :description_ar WHERE id = :id";
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            ':description_ar' => $updates['description_ar'],
            ':id' => $product_id
        ]);
        echo "Produit ID $product_id corrigé: description_ar = '{$updates['description_ar']}'\n";
    }
    
    // Vérifier et afficher les données corrigées
    echo "\nVérification des données après correction:\n";
    $products = $conn->query("SELECT id, name, description_en, description_ar FROM products ORDER BY id")->fetchAll();
    
    foreach ($products as $product) {
        echo "ID: {$product['id']}, Nom: {$product['name']}, EN: {$product['description_en']}, AR: {$product['description_ar']}\n";
    }
    
    echo "\nCorrection terminée avec succès!\n";
    
} catch (Exception $e) {
    echo "Erreur lors de la correction: " . $e->getMessage() . "\n";
    file_put_contents('debug.log', "fix_database_data.php: {$e->getMessage()}\n", FILE_APPEND);
}
?>
