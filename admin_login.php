<?php
// admin_login.php
require_once 'db_connect.php';

// Regenerate session ID to prevent session fixation
session_regenerate_id(true);

$error = '';
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_SPECIAL_CHARS);
    $password = $_POST['password']; // Password is not sanitized as it’s compared directly

    if (!empty($username) && !empty($password)) {
        try {
            $db = Database::getInstance();
            $conn = $db->getConnection();
            $stmt = $conn->prepare("SELECT id, username, password FROM admins WHERE username = :username");
            $stmt->execute([':username' => $username]);
            $admin = $stmt->fetch();

            if ($admin && password_verify($password, $admin['password'])) { // Use password_verify for security
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                header("Location: admin.php");
                exit;
            } else {
                $error = "Invalid username or password.";
                file_put_contents('debug.log', "admin_login.php: Failed login attempt for username=$username at " . date('Y-m-d H:i:s') . "\n", FILE_APPEND);
            }
        } catch (Exception $e) {
            $error = "Unable to connect to the database. Please try again later.";
            file_put_contents('debug.log', "admin_login.php: {$e->getMessage()}\n", FILE_APPEND);
        }
    } else {
        $error = "Please fill in all fields.";
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - Madjour Industries</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .login-container {
            max-width: 400px;
            margin: 5rem auto;
            padding: 2rem;
            background: var(--white);
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
        }
        .login-form {
            display: grid;
            gap: 1rem;
        }
        .login-form label {
            color: var(--dark-gray);
            font-weight: 500;
        }
        .login-form input {
            padding: 0.75rem;
            border: 2px solid var(--light-gray);
            border-radius: var(--border-radius);
            font-size: 1rem;
        }
        .login-form button {
            background: var(--primary-red);
            color: var(--white);
            border: none;
            padding: 0.75rem;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: var(--transition);
        }
        .login-form button:hover {
            background: #B71C1C;
        }
        .error {
            color: #ef4444;
            font-size: 0.9rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h2 style="color: var(--primary-red); text-align: center;">Admin Login</h2>
        <?php if (!empty($error)): ?>
            <p class="error"><?php echo htmlspecialchars($error); ?></p>
        <?php endif; ?>
        <form class="login-form" method="POST">
            <label for="username">Username</label>
            <input type="text" id="username" name="username" required>
            
            <label for="password">Password</label>
            <input type="password" id="password" name="password" required>
            
            <button type="submit">Login</button>
        </form>
    </div>
</body>
</html>