-- Script d'optimisation UTF-8 pour la base de données Madjour Industries
-- À exécuter pour s'assurer que la base de données supporte parfaitement tous les caractères spéciaux

-- 1. Modifier la base de données pour utiliser utf8mb4
ALTER DATABASE madjour_industries CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

-- 2. Optimiser la table categories
ALTER TABLE categories CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE categories MODIFY COLUMN name_en VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;
ALTER TABLE categories MODIFY COLUMN name_ar VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;

-- 3. Optimiser la table products
ALTER TABLE products CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE products MODIFY COLUMN name VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;
ALTER TABLE products MODIFY COLUMN description_en TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;
ALTER TABLE products MODIFY COLUMN description_ar TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;
ALTER TABLE products MODIFY COLUMN image_path VARCHAR(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT 'Uploads/placeholder.jpg';

-- 4. Optimiser la table admins (si elle existe)
ALTER TABLE admins CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE admins MODIFY COLUMN username VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;

-- 5. Vérifier les modifications
SELECT 
    TABLE_NAME,
    TABLE_COLLATION,
    COLUMN_NAME,
    CHARACTER_SET_NAME,
    COLLATION_NAME
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'madjour_industries' 
    AND TABLE_NAME IN ('categories', 'products', 'admins')
    AND DATA_TYPE IN ('varchar', 'text', 'char')
ORDER BY TABLE_NAME, COLUMN_NAME;

-- 6. Créer des index optimisés pour la recherche multilingue
-- Index pour la recherche de produits
CREATE INDEX idx_products_name_search ON products (name(50));
CREATE INDEX idx_products_desc_en_search ON products (description_en(100));
CREATE INDEX idx_products_desc_ar_search ON products (description_ar(100));

-- Index pour la recherche de catégories
CREATE INDEX idx_categories_name_en_search ON categories (name_en(50));
CREATE INDEX idx_categories_name_ar_search ON categories (name_ar(50));

-- 7. Exemples de données de test avec caractères spéciaux (optionnel)
-- Décommentez pour tester l'insertion de caractères spéciaux

/*
-- Test d'insertion de catégories avec caractères spéciaux
INSERT INTO categories (name_en, name_ar) VALUES 
('Beauty & Care', 'الجمال والعناية'),
('Home & Garden', 'المنزل والحديقة'),
('Electronics & Tech', 'الإلكترونيات والتكنولوجيا');

-- Test d'insertion de produits avec caractères spéciaux
INSERT INTO products (name, category_id, description_en, description_ar, image_path) VALUES 
('Café Premium ☕', 1, 'High-quality coffee with special aroma & taste', 'قهوة عالية الجودة بنكهة ورائحة مميزة ☕', 'Uploads/placeholder.jpg'),
('Naïve Shampoo', 1, 'Gentle shampoo for sensitive skin (pH 5.5)', 'شامبو لطيف للبشرة الحساسة (درجة حموضة 5.5)', 'Uploads/placeholder.jpg'),
('Product™ 2024', 2, 'Innovative product with trademark & copyright ©', 'منتج مبتكر بعلامة تجارية وحقوق طبع ونشر ©', 'Uploads/placeholder.jpg');
*/

-- 8. Requêtes de vérification pour s'assurer que tout fonctionne
-- Vérifier l'encodage des données existantes
SELECT 
    'categories' as table_name,
    name_en,
    name_ar,
    CHAR_LENGTH(name_en) as en_char_length,
    CHAR_LENGTH(name_ar) as ar_char_length,
    LENGTH(name_en) as en_byte_length,
    LENGTH(name_ar) as ar_byte_length
FROM categories
LIMIT 5;

SELECT 
    'products' as table_name,
    name,
    description_en,
    description_ar,
    CHAR_LENGTH(name) as name_char_length,
    LENGTH(name) as name_byte_length
FROM products
LIMIT 5;

-- 9. Configuration recommandée pour my.cnf (à ajouter manuellement)
/*
[client]
default-character-set = utf8mb4

[mysql]
default-character-set = utf8mb4

[mysqld]
character-set-client-handshake = FALSE
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'
*/

-- 10. Commandes de vérification finale
SHOW VARIABLES LIKE 'character_set%';
SHOW VARIABLES LIKE 'collation%';

-- Afficher un message de confirmation
SELECT 'Base de données optimisée pour UTF-8MB4 avec succès!' as message;
