<?php
// Test pour vérifier la conservation des espaces et sauts de ligne dans les descriptions
require_once 'db_connect.php';

try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // Test d'insertion d'une description avec espaces et sauts de ligne
    $test_description_en = "This is a test description.

It has multiple lines.
    And some indented text.
        Even more indented text.

And some normal text again.";

    $test_description_ar = "هذا وصف تجريبي.

يحتوي على عدة أسطر.
    وبعض النص المسافة البادئة.
        نص أكثر مسافة بادئة.

وبعض النص العادي مرة أخرى.";

    echo "<h2>Test de conservation des espaces et sauts de ligne</h2>";
    echo "<h3>Description en anglais (original):</h3>";
    echo "<pre>" . htmlspecialchars($test_description_en) . "</pre>";
    
    echo "<h3>Description en arabe (original):</h3>";
    echo "<pre>" . htmlspecialchars($test_description_ar) . "</pre>";
    
    // Simuler le traitement comme dans admin.php
    $processed_description_en = filter_var($test_description_en, FILTER_UNSAFE_RAW);
    $processed_description_ar = filter_var($test_description_ar, FILTER_UNSAFE_RAW);
    
    echo "<h3>Description en anglais (après traitement):</h3>";
    echo "<pre>" . htmlspecialchars($processed_description_en) . "</pre>";
    
    echo "<h3>Description en arabe (après traitement):</h3>";
    echo "<pre>" . htmlspecialchars($processed_description_ar) . "</pre>";
    
    // Test d'affichage avec white-space: pre-wrap
    echo "<h3>Affichage avec white-space: pre-wrap (anglais):</h3>";
    echo "<div style='white-space: pre-wrap; border: 1px solid #ccc; padding: 10px; background: #f9f9f9;'>" . htmlspecialchars($processed_description_en) . "</div>";
    
    echo "<h3>Affichage avec white-space: pre-wrap (arabe):</h3>";
    echo "<div style='white-space: pre-wrap; border: 1px solid #ccc; padding: 10px; background: #f9f9f9; direction: rtl;'>" . htmlspecialchars($processed_description_ar) . "</div>";
    
    echo "<h3>Validation UTF-8:</h3>";
    echo "Description EN valide UTF-8: " . (mb_check_encoding($processed_description_en, 'UTF-8') ? "✓ Oui" : "✗ Non") . "<br>";
    echo "Description AR valide UTF-8: " . (mb_check_encoding($processed_description_ar, 'UTF-8') ? "✓ Oui" : "✗ Non") . "<br>";
    
} catch (Exception $e) {
    echo "Erreur: " . htmlspecialchars($e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test de formatage des descriptions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h2, h3 {
            color: #D32F2F;
        }
        pre {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
</body>
</html>
