<?php
/**
 * Script de test pour vérifier la gestion des caractères spéciaux
 * À exécuter après avoir appliqué toutes les corrections
 */

require_once 'db_connect.php';

// Fonction pour afficher les résultats de test
function displayTestResult($testName, $success, $message = '') {
    $status = $success ? '✅ SUCCÈS' : '❌ ÉCHEC';
    echo "<div style='margin: 10px 0; padding: 10px; border-left: 4px solid " . 
         ($success ? '#10b981' : '#ef4444') . "; background: " . 
         ($success ? '#f0fdf4' : '#fef2f2') . ";'>";
    echo "<strong>$status - $testName</strong>";
    if ($message) echo "<br>$message";
    echo "</div>";
}

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des Caractères Spéciaux - Madjour Industries</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .test-data { background: #f8f9fa; padding: 15px; border-radius: 4px; margin: 10px 0; }
        .test-form { background: #fff; padding: 20px; border: 1px solid #ddd; border-radius: 8px; margin: 20px 0; }
        input, textarea, select { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #D32F2F; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #B71C1C; }
    </style>
</head>
<body>
    <h1>🧪 Test des Caractères Spéciaux - Madjour Industries</h1>
    
    <?php
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        
        echo "<div class='test-section'>";
        echo "<h2>1. Test de Connexion à la Base de Données</h2>";
        displayTestResult("Connexion UTF-8", true, "Connexion établie avec charset utf8mb4");
        
        // Test de la configuration UTF-8
        $charset_result = $conn->query("SHOW VARIABLES LIKE 'character_set_connection'")->fetch();
        $collation_result = $conn->query("SHOW VARIABLES LIKE 'collation_connection'")->fetch();
        
        displayTestResult(
            "Configuration Charset", 
            $charset_result['Value'] === 'utf8mb4',
            "Charset: " . $charset_result['Value']
        );
        
        displayTestResult(
            "Configuration Collation", 
            strpos($collation_result['Value'], 'utf8mb4') !== false,
            "Collation: " . $collation_result['Value']
        );
        echo "</div>";
        
        // Test des données existantes
        echo "<div class='test-section'>";
        echo "<h2>2. Test des Données Existantes</h2>";
        
        $categories = $conn->query("SELECT * FROM categories LIMIT 3")->fetchAll();
        $products = $conn->query("SELECT * FROM products LIMIT 3")->fetchAll();
        
        echo "<h3>Catégories:</h3>";
        foreach ($categories as $cat) {
            echo "<div class='test-data'>";
            echo "<strong>EN:</strong> " . htmlspecialchars($cat['name_en']) . "<br>";
            echo "<strong>AR:</strong> " . htmlspecialchars($cat['name_ar']) . "<br>";
            echo "<small>Longueur EN: " . mb_strlen($cat['name_en'], 'UTF-8') . " caractères, " . 
                 "AR: " . mb_strlen($cat['name_ar'], 'UTF-8') . " caractères</small>";
            echo "</div>";
        }
        
        echo "<h3>Produits:</h3>";
        foreach ($products as $prod) {
            echo "<div class='test-data'>";
            echo "<strong>Nom:</strong> " . htmlspecialchars($prod['name']) . "<br>";
            echo "<strong>Description EN:</strong> " . htmlspecialchars(substr($prod['description_en'], 0, 100)) . "...<br>";
            echo "<strong>Description AR:</strong> " . htmlspecialchars(substr($prod['description_ar'], 0, 100)) . "...<br>";
            echo "</div>";
        }
        echo "</div>";
        
    } catch (Exception $e) {
        displayTestResult("Connexion Base de Données", false, $e->getMessage());
    }
    ?>
    
    <div class="test-section">
        <h2>3. Test d'Insertion de Caractères Spéciaux</h2>
        <div class="test-form">
            <h3>Formulaire de Test - Catégorie</h3>
            <form method="POST" action="">
                <input type="hidden" name="test_category" value="1">
                <label>Nom EN (avec caractères spéciaux):</label>
                <input type="text" name="test_name_en" value="Beauty & Care™ (2024)" placeholder="Ex: Café & Thé, Beauty™, Test (2024)">
                
                <label>Nom AR (avec caractères arabes):</label>
                <input type="text" name="test_name_ar" value="الجمال والعناية ™ (٢٠٢٤)" placeholder="Ex: القهوة والشاي، الجمال™">
                
                <button type="submit">Tester l'Insertion</button>
            </form>
        </div>
        
        <div class="test-form">
            <h3>Formulaire de Test - Produit</h3>
            <form method="POST" action="">
                <input type="hidden" name="test_product" value="1">
                <label>Nom du Produit:</label>
                <input type="text" name="test_product_name" value="Café Premium ☕ & Thé 🍵" placeholder="Ex: Produit™, Café☕, Test & Co.">
                
                <label>Description EN:</label>
                <textarea name="test_desc_en" rows="3" placeholder="Description with special chars: &, ©, ™, €, etc.">High-quality coffee ☕ with special aroma & taste. Price: 15€. Copyright © 2024.</textarea>
                
                <label>Description AR:</label>
                <textarea name="test_desc_ar" rows="3" placeholder="وصف بالعربية مع رموز خاصة">قهوة عالية الجودة ☕ بنكهة ورائحة مميزة. السعر: ١٥€. حقوق الطبع © ٢٠٢٤.</textarea>
                
                <select name="test_category_id">
                    <?php
                    if (isset($categories)) {
                        foreach ($categories as $cat) {
                            echo "<option value='{$cat['id']}'>" . htmlspecialchars($cat['name_en']) . "</option>";
                        }
                    }
                    ?>
                </select>
                
                <button type="submit">Tester l'Insertion</button>
            </form>
        </div>
    </div>
    
    <?php
    // Traitement des tests d'insertion
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        echo "<div class='test-section'>";
        echo "<h2>4. Résultats des Tests d'Insertion</h2>";
        
        try {
            if (isset($_POST['test_category'])) {
                $name_en = trim($_POST['test_name_en']);
                $name_ar = trim($_POST['test_name_ar']);
                
                // Validation UTF-8
                $utf8_valid = mb_check_encoding($name_en, 'UTF-8') && mb_check_encoding($name_ar, 'UTF-8');
                displayTestResult("Validation UTF-8 Catégorie", $utf8_valid);
                
                if ($utf8_valid && !empty($name_en) && !empty($name_ar)) {
                    $stmt = $conn->prepare("INSERT INTO categories (name_en, name_ar) VALUES (:name_en, :name_ar)");
                    $result = $stmt->execute([':name_en' => $name_en, ':name_ar' => $name_ar]);
                    
                    displayTestResult(
                        "Insertion Catégorie", 
                        $result,
                        "Catégorie insérée: EN='$name_en', AR='$name_ar'"
                    );
                }
            }
            
            if (isset($_POST['test_product'])) {
                $name = trim($_POST['test_product_name']);
                $desc_en = trim($_POST['test_desc_en']);
                $desc_ar = trim($_POST['test_desc_ar']);
                $category_id = (int)$_POST['test_category_id'];
                
                // Validation UTF-8
                $utf8_valid = mb_check_encoding($name, 'UTF-8') && 
                             mb_check_encoding($desc_en, 'UTF-8') && 
                             mb_check_encoding($desc_ar, 'UTF-8');
                displayTestResult("Validation UTF-8 Produit", $utf8_valid);
                
                if ($utf8_valid && !empty($name) && !empty($desc_en) && !empty($desc_ar)) {
                    $stmt = $conn->prepare("INSERT INTO products (name, category_id, description_en, description_ar, image_path) VALUES (:name, :category_id, :desc_en, :desc_ar, 'Uploads/placeholder.jpg')");
                    $result = $stmt->execute([
                        ':name' => $name,
                        ':category_id' => $category_id,
                        ':desc_en' => $desc_en,
                        ':desc_ar' => $desc_ar
                    ]);
                    
                    displayTestResult(
                        "Insertion Produit", 
                        $result,
                        "Produit inséré: '$name'"
                    );
                }
            }
        } catch (Exception $e) {
            displayTestResult("Test d'Insertion", false, $e->getMessage());
        }
        
        echo "</div>";
    }
    ?>
    
    <div class="test-section">
        <h2>5. Caractères de Test Recommandés</h2>
        <div class="test-data">
            <h3>Caractères Spéciaux à Tester:</h3>
            <p><strong>Français:</strong> àáâãäåæçèéêëìíîïñòóôõöøùúûüý</p>
            <p><strong>Symboles:</strong> ©™®€£¥§¶†‡•…‰‹›""''–—</p>
            <p><strong>Arabe:</strong> العربية، منتج، فئة، وصف، جودة، مميز</p>
            <p><strong>Emojis:</strong> ☕🍵🏠🌟💡🔧⚡🎯📱💻</p>
            <p><strong>Ponctuation:</strong> & (ampersand), " (guillemets), ' (apostrophe), - (tiret)</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>6. Instructions de Test Manuel</h2>
        <ol>
            <li>Utilisez le panneau d'administration pour ajouter des catégories avec caractères spéciaux</li>
            <li>Ajoutez des produits avec descriptions multilingues</li>
            <li>Testez l'édition des données existantes</li>
            <li>Vérifiez l'affichage sur la page principale</li>
            <li>Testez la recherche avec caractères spéciaux</li>
        </ol>
    </div>
</body>
</html>
