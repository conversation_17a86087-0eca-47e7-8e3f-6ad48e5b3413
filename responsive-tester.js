/**
 * Responsive Tester for Madjour Industries AppBar
 * Permet de tester rapidement différentes tailles d'écran
 */

class ResponsiveTester {
    constructor() {
        this.presets = {
            'Desktop Large': { width: 1920, height: 1080 },
            'Desktop': { width: 1366, height: 768 },
            'Laptop': { width: 1024, height: 768 },
            'Tablet Portrait': { width: 768, height: 1024 },
            'Tablet Landscape': { width: 1024, height: 768 },
            'Mobile Large': { width: 414, height: 896 },
            'Mobile Medium': { width: 375, height: 667 },
            'Mobile Small': { width: 320, height: 568 }
        };

        this.init();
    }

    init() {
        this.createTesterUI();
        this.addEventListeners();
        this.updateCurrentSize();
    }

    createTesterUI() {
        // Créer le panneau de test
        const testerPanel = document.createElement('div');
        testerPanel.id = 'responsive-tester';
        testerPanel.innerHTML = `
            <div class="tester-header">
                <h3>📱 Responsive Tester</h3>
                <button class="tester-toggle">−</button>
            </div>
            <div class="tester-content">
                <div class="current-size">
                    <strong>Taille actuelle:</strong> <span id="current-dimensions"></span>
                </div>
                <div class="presets">
                    ${Object.entries(this.presets).map(([name, size]) =>
                        `<button class="preset-btn" data-width="${size.width}" data-height="${size.height}">
                            ${name}<br><small>${size.width}×${size.height}</small>
                        </button>`
                    ).join('')}
                </div>
                <div class="custom-size">
                    <input type="number" id="custom-width" placeholder="Largeur" min="320" max="1920">
                    <input type="number" id="custom-height" placeholder="Hauteur" min="568" max="1080">
                    <button id="apply-custom">Appliquer</button>
                </div>
            </div>
        `;

        // Ajouter les styles
        const styles = document.createElement('style');
        styles.textContent = `
            #responsive-tester {
                position: fixed;
                top: 20px;
                left: 20px;
                background: white;
                border: 2px solid #D32F2F;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10001;
                font-family: Arial, sans-serif;
                font-size: 12px;
                max-width: 300px;
            }

            .tester-header {
                background: #D32F2F;
                color: white;
                padding: 8px 12px;
                display: flex;
                justify-content: space-between;
                align-items: center;
                cursor: move;
            }

            .tester-header h3 {
                margin: 0;
                font-size: 14px;
            }

            .tester-toggle {
                background: none;
                border: none;
                color: white;
                font-size: 16px;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
            }

            .tester-content {
                padding: 12px;
            }

            .current-size {
                margin-bottom: 12px;
                padding: 8px;
                background: #f5f5f5;
                border-radius: 4px;
            }

            .presets {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 6px;
                margin-bottom: 12px;
            }

            .preset-btn {
                padding: 8px 6px;
                border: 1px solid #ddd;
                background: white;
                border-radius: 4px;
                cursor: pointer;
                font-size: 10px;
                text-align: center;
                transition: all 0.2s;
            }

            .preset-btn:hover {
                background: #D32F2F;
                color: white;
                border-color: #D32F2F;
            }

            .preset-btn small {
                color: #666;
                font-size: 9px;
            }

            .preset-btn:hover small {
                color: #fff;
            }

            .custom-size {
                display: flex;
                gap: 4px;
                flex-wrap: wrap;
            }

            .custom-size input {
                flex: 1;
                padding: 6px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 11px;
                min-width: 60px;
            }

            #apply-custom {
                padding: 6px 12px;
                background: #D32F2F;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                font-size: 11px;
            }

            .tester-content.collapsed {
                display: none;
            }

            @media (max-width: 768px) {
                #responsive-tester {
                    position: relative;
                    top: auto;
                    left: auto;
                    margin: 10px;
                    max-width: none;
                }
            }
        `;

        document.head.appendChild(styles);
        document.body.appendChild(testerPanel);
    }

    addEventListeners() {
        // Toggle panel
        document.querySelector('.tester-toggle').addEventListener('click', () => {
            const content = document.querySelector('.tester-content');
            const toggle = document.querySelector('.tester-toggle');
            content.classList.toggle('collapsed');
            toggle.textContent = content.classList.contains('collapsed') ? '+' : '−';
        });

        // Preset buttons
        document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const width = parseInt(btn.dataset.width);
                const height = parseInt(btn.dataset.height);
                this.resizeWindow(width, height);
            });
        });

        // Custom size
        document.getElementById('apply-custom').addEventListener('click', () => {
            const width = parseInt(document.getElementById('custom-width').value);
            const height = parseInt(document.getElementById('custom-height').value);
            if (width && height) {
                this.resizeWindow(width, height);
            }
        });

        // Update size on window resize
        window.addEventListener('resize', () => {
            this.updateCurrentSize();
        });

        // Make panel draggable
        this.makeDraggable();
    }

    resizeWindow(width, height) {
        if (window.resizeTo) {
            window.resizeTo(width, height);
        } else {
            // Fallback for browsers that don't support resizeTo
            console.log(`Resize to: ${width}×${height}`);
            alert(`Redimensionnez manuellement la fenêtre à ${width}×${height}px`);
        }
        setTimeout(() => this.updateCurrentSize(), 100);
    }

    updateCurrentSize() {
        const width = window.innerWidth;
        const height = window.innerHeight;
        document.getElementById('current-dimensions').textContent = `${width}×${height}px`;

        // Highlight current breakpoint
        let breakpoint = 'Desktop Large';
        if (width <= 480) breakpoint = 'Small Mobile';
        else if (width <= 768) breakpoint = 'Mobile';
        else if (width <= 1024) breakpoint = 'Tablet';
        else if (width <= 1366) breakpoint = 'Desktop';

        document.getElementById('current-dimensions').textContent += ` (${breakpoint})`;
    }

    makeDraggable() {
        const panel = document.getElementById('responsive-tester');
        const header = document.querySelector('.tester-header');
        let isDragging = false;
        let currentX;
        let currentY;
        let initialX;
        let initialY;
        let xOffset = 0;
        let yOffset = 0;

        header.addEventListener('mousedown', dragStart);
        document.addEventListener('mousemove', drag);
        document.addEventListener('mouseup', dragEnd);

        function dragStart(e) {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;

            if (e.target === header || header.contains(e.target)) {
                isDragging = true;
            }
        }

        function drag(e) {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;

                xOffset = currentX;
                yOffset = currentY;

                panel.style.transform = `translate3d(${currentX}px, ${currentY}px, 0)`;
            }
        }

        function dragEnd() {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
        }
    }
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => new ResponsiveTester());
} else {
    new ResponsiveTester();
}