const translations = {
    en: {
        'nav.home': 'Home',
        'nav.categories': 'Categories',
        'nav.products': 'Products',
        'nav.about': 'About Us',
        'nav.contact': 'Contact',
        'categories.title': 'Categories',
        'categories.lighters': 'Lighters & Gas',
        'categories.lighters_desc': 'Quality lighters and gas refills',
        'categories.toothbrushes': 'Toothbrushes',
        'categories.toothbrushes_desc': 'Oral care essentials',
        'categories.glue': 'Adhesive Glue',
        'categories.glue_desc': 'Strong adhesive solutions',
        'categories.baby': 'Baby Products',
        'categories.baby_desc': 'Safe products for babies',
        'categories.insecticides': 'Insecticides',
        'categories.insecticides_desc': 'Effective pest control',
        'hero.title': 'Trusted Products for Everyday Life',
        'hero.subtitle': 'Madjour Industries Quality You Can Count On',
        'products.title': 'Our Products',
        'products.view_details': 'View Details',
        'products.premium_lighter': 'Madjour Lighter',
        'products.gas_refill': 'Universal Gas Refill',
        'products.soft_toothbrush': 'Soft Bristle Toothbrush',
        'products.medium_toothbrush': 'toothbrush for children',
        'products.super_glue': 'Super Strong Adhesive',
        'products.baby_Pump': 'Manual Breast Pump',
        'products.baby_Bottles': 'Baby Bottles',
        'products.mosquito_spray': 'Insect repellent spray',
        'products.mosquito_bait': 'Insect bait',
        'about.title': 'About EURL MADJOUR HICHEM',
        'about.description': 'EURL MADJOUR HICHEM is a leading company in multiple sectors, comprising several manufacturing units, including lighters, gas, glue, insecticides, and semi-pharmaceutical materials.',
        'about.feature1': 'Lighters Manufacturing',
        'about.feature1_desc': 'High-quality lighter production with strict quality control',
        'about.feature2': 'Gas Production',
        'about.feature2_desc': 'Safe and efficient gas manufacturing solutions',
        'about.feature3': 'Industrial Materials',
        'about.feature3_desc': 'Production of glue, insecticides, and semi-pharmaceuticals',
        'footer.quick_links': 'Quick Links',
        'footer.policies': 'Policies',
        'footer.privacy': 'Privacy Policy',
        'footer.terms': 'Terms & Conditions',
        'footer.shipping': 'Shipping Policy',
        'footer.returns': 'Returns & Refunds',
        'footer.contact_title': 'Contact Info',
        'footer.email': 'Email:',
        'footer.phone': 'Phone:',
        'footer.address': 'Address:',
        'footer.rights': 'All rights reserved.',
        'modal.close': 'Close',
        'contact.sales': 'Sales Service',
        'contact.facebook': 'Facebook',
        'contact.tiktok': 'TikTok',
        'search.placeholder': 'Search products...',
        'search.no_results': 'No products found for "%s"',
        'search.empty': 'Please enter a search term',
        // Product descriptions
        'products.madjour_lighter_desc': 'A high-quality plastic gas lighter designed for daily use with ease and safety. It features a heat-resistant plastic body and an electronic ignition system (Piezoelectric) for reliable performance. Available in vibrant colors, adding a lively touch to its appearance. Ideal for quick and safe use at home or on the go. The design includes a compact size for easy carrying in a pocket or bag.',
        'products.madjour_star_toothbrush_desc': 'Designed for deep and effective teeth cleaning while maintaining gum health. Features multi-directional soft bristles to reach tight spaces without causing irritation and an ergonomic handle for excellent control.',
        'products.medium_toothbrush_desc': 'A medium bristle toothbrush for thorough cleaning, featuring multi-directional bristles and an ergonomic handle for optimal control.',
        'products.madjour_super_glue_desc': 'A high-quality instant adhesive ideal for household and professional use. Its effective formula ensures strong, fast, and long-lasting bonding on various surfaces like plastic, metal, ceramic, wood, and rubber.',
        'products.baby_Pump_desc': 'This is a manual breast pump designed for comfortable and efficient milk expression. It features a soft, flexible silicone shield that fits comfortably over the breast, a manual handle for controlled suction, and a detachable container for storing expressed milk. The ergonomic design ensures ease of use, making it an ideal choice for nursing mothers.',
        'products.baby_Bottles_desc': 'This set includes three baby bottles with a capacity suitable for infants, featuring a contoured design for easy gripping. Each bottle has a different colored ring (blue, green, and pink) for easy identification and comes with a silicone nipple for smooth feeding. The bottles are made from safe, BPA-free material, ensuring the health and safety of the baby.',
        'products.mosquito_spray_desc': 'This insecticide product is designed to eliminate and control a wide range of insects, including mosquitoes and flies. It contains a fast-acting formula that provides long-lasting protection, ensuring a pest-free environment. The product is easy to use thanks to the convenient application method, such as spray or bait, and is designed to be safe when used according to instructions. Ideal for household use, it helps maintain cleanliness and comfort.',
    },
    fr: {
        'nav.home': 'Accueil',
        'nav.categories': 'Catégories',
        'nav.products': 'Produits',
        'nav.about': 'À Propos',
        'nav.contact': 'Contact',
        'categories.title': 'Catégories',
        'categories.lighters': 'Briquets & Gaz',
        'categories.lighters_desc': 'Briquets de qualité et recharges de gaz',
        'categories.toothbrushes': 'Brosses à Dents',
        'categories.toothbrushes_desc': 'Essentiels de soins bucco-dentaires',
        'categories.glue': 'Colle Adhésive',
        'categories.glue_desc': 'Solutions adhésives fortes',
        'categories.baby': 'Produits Bébé',
        'categories.baby_desc': 'Produits sûrs pour bébés',
        'categories.insecticides': 'Insecticides',
        'categories.insecticides_desc': 'Contrôle efficace des nuisibles',
        'hero.title': 'Produits de Confiance pour la Vie Quotidienne',
        'hero.subtitle': 'Qualité Madjour Industries sur laquelle vous pouvez compter',
        'products.title': 'Nos Produits',
        'products.view_details': 'Voir Détails',
        'products.premium_lighter': 'Briquet Madjour',
        'products.soft_toothbrush': 'Brosse à Dents Souple',
        'products.medium_toothbrush': 'Brosse à dents pour enfants',
        'products.super_glue': 'Adhésif Super Fort',
        'products.baby_Pump': 'Tire-lait Manuel',
        'products.baby_Bottles': 'Biberons pour Bébé',
        'products.mosquito_spray': 'Répulsif pour insectes',
        'products.mosquito_bait': 'Appât pour insectes',
        'about.title': 'À Propos de EURL MADJOUR HICHEM',
        'about.description': 'EURL MADJOUR HICHEM est une entreprise de premier plan dans plusieurs secteurs, comprenant plusieurs unités de fabrication, notamment des briquets, du gaz, de la colle, des insecticides et des matériaux semi-pharmaceutiques.',
        'about.feature1': 'Fabrication de Briquets',
        'about.feature1_desc': 'Production de briquets de haute qualité avec contrôle qualité strict',
        'about.feature2': 'Production de Gaz',
        'about.feature2_desc': 'Solutions de fabrication de gaz sûres et efficaces',
        'about.feature3': 'Matériaux Industriels',
        'about.feature3_desc': 'Production de colle, insecticides et matériaux semi-pharmaceutiques',
        'footer.quick_links': 'Liens Rapides',
        'footer.policies': 'Politiques',
        'footer.privacy': 'Politique de Confidentialité',
        'footer.terms': 'Termes & Conditions',
        'footer.shipping': 'Politique d\'Expédition',
        'footer.returns': 'Retours & Remboursements',
        'footer.contact_title': 'Informations de Contact',
        'footer.email': 'Email:',
        'footer.phone': 'Téléphone:',
        'footer.address': 'Adresse:',
        'footer.rights': 'Tous droits réservés.',
        'modal.close': 'Fermer',
        'contact.sales': 'Service de Vente',
        'contact.facebook': 'Facebook',
        'contact.tiktok': 'TikTok',
        'search.placeholder': 'Rechercher des produits...',
        'search.no_results': 'Aucun produit trouvé pour "%s"',
        'search.empty': 'Veuillez entrer un terme de recherche',
        // Product descriptions
        'products.madjour_lighter_desc': 'Un briquet à gaz en plastique de haute qualité, conçu pour un usage quotidien avec facilité et sécurité. Il dispose d’un corps en plastique résistant à la chaleur et d’un système d’allumage électronique (piézoélectrique) pour des performances fiables. Disponible en couleurs vives, ajoutant une touche de vivacité à son apparence. Idéal pour une utilisation rapide et sûre à la maison ou en déplacement. Le design inclut une taille compacte pour un transport facile dans une poche ou un sac.',
        'products.madjour_star_toothbrush_desc': 'Conçue pour un nettoyage profond et efficace des dents tout en préservant la santé des gencives. Dispose de poils doux multidirectionnels pour atteindre les espaces étroits sans causer d’irritation et d’un manche ergonomique pour un contrôle excellent.',
        'products.medium_toothbrush_desc': 'Une brosse à dents à poils moyens pour un nettoyage en profondeur, dotée de poils multidirectionnels et d’une poignée ergonomique pour un contrôle optimal.',
        'products.madjour_super_glue_desc': 'Un adhésif instantané de haute qualité idéal pour les usages domestiques et professionnels. Sa formule efficace garantit un collage fort, rapide et durable sur diverses surfaces comme le plastique, le métal, la céramique, le bois et le caoutchouc.',
        'products.baby_Pump_desc': 'Ce tire-lait manuel est conçu pour une expression de lait confortable et efficace. Il dispose d’un embout en silicone souple et flexible qui s’adapte confortablement au sein, d’une poignée manuelle pour un contrôle de la succion, et d’un récipient détachable pour stocker le lait exprimé. Son design ergonomique garantit une utilisation facile, idéal pour les mères qui allaitent.',
        'products.baby_Bottles_desc': 'Ce set comprend trois biberons d’une capacité adaptée aux nourrissons, avec un design contouré pour une prise en main facile. Chaque biberon est doté d’un anneau de couleur différente (bleu, vert et rose) pour une identification facile et d’une tétine en silicone pour une alimentation fluide. Les biberons sont fabriqués dans un matériau sans BPA, assurant la santé et la sécurité du bébé.',
        'products.mosquito_spray_desc': 'Ce produit insecticide a été conçu pour éliminer et contrôler une large gamme d\'insectes, y compris les moustiques et les mouches. Il contient une formule à effet rapide qui offre une protection durable, garantissant un environnement sans nuisibles. Le produit est facile à utiliser grâce à la méthode d\'application pratique, comme le spray ou l\'appât, et est conçu pour être sécuritaire lorsqu\'il est utilisé conformément aux instructions. Idéal pour un usage domestique, il contribue à maintenir la propreté et le confort.',
    }
};

// Current language state
let currentLang = 'en';

// DOM elements
const elements = {
    langButtons: document.querySelectorAll('.lang-btn'),
    searchBar: document.querySelector('.search-bar'),
    searchBtn: document.querySelector('.search-btn'),
    mobileMenuBtn: document.querySelector('.mobile-menu-btn'),
    navMenu: document.querySelector('.nav-menu'),
    navLinks: document.querySelectorAll('.nav-link'),
    categoryTabs: document.querySelectorAll('.category-tab'),
    viewDetailsButtons: document.querySelectorAll('.view-details-btn'),
    productModal: document.getElementById('product-modal'),
    modalCloseButtons: document.querySelectorAll('.modal-close-btn'),
    modalTitle: document.querySelector('.modal-title'),
    modalImage: document.querySelector('.modal-product-image'),
    aboutTitle: document.querySelector('.about-title'),
    aboutDescription: document.querySelector('.about-description'),
    aboutFeatures: document.querySelectorAll('.feature h3'),
    aboutFeatureDescriptions: document.querySelectorAll('.feature p'),
    body: document.body
};

// Initialize the application
function init() {
    if (!Object.keys(elements).every(key => elements[key] || key === 'viewDetailsButtons')) {
        console.error('Missing critical DOM elements. Application initialization aborted.');
        return;
    }
    
    setupEventListeners();
    setupIntersectionObserver();
    setupSmoothScrolling();
    updateLanguage(currentLang);
    
    // Add loading animation to elements
    const animatedElements = document.querySelectorAll('.product-card, .feature-item');
    animatedElements.forEach((el, index) => {
        el.style.animationDelay = `${index * 0.1}s`;
        el.classList.add('fade-in');
    });

    // Optimize images
    optimizeImages();
}

// Setup all event listeners
function setupEventListeners() {
    // Language switcher
    elements.langButtons.forEach(btn => btn.addEventListener('click', handleLanguageSwitch));

    // Search functionality
    if (elements.searchBtn && elements.searchBar) {
        elements.searchBtn.addEventListener('click', handleSearch);
        elements.searchBar.addEventListener('keypress', e => {
            if (e.key === 'Enter') handleSearch();
        });
    }

    // Mobile menu toggle
    if (elements.mobileMenuBtn && elements.navMenu) {
        elements.mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }

    // Navigation links
    elements.navLinks.forEach(link => link.addEventListener('click', handleNavClick));

    // Category tabs
    elements.categoryTabs.forEach(tab => {
        tab.addEventListener('click', handleCategoryTabClick);
        tab.addEventListener('keypress', e => {
            if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                handleCategoryTabClick.call(tab);
            }
        });
    });

    // View details buttons (delegated event listener)
    document.addEventListener('click', e => {
        if (e.target.classList.contains('view-details-btn')) handleViewDetails(e);
    });

    // Modal close buttons
    elements.modalCloseButtons.forEach(btn => btn.addEventListener('click', closeProductModal));

    // Close modal with Escape key
    document.addEventListener('keydown', e => {
        if (e.key === 'Escape' && elements.productModal?.classList.contains('active')) {
            closeProductModal();
        }
    });

    // Trap focus in modal
    if (elements.productModal) {
        elements.productModal.addEventListener('keydown', handleModalFocusTrap);
    }

    // Debounced resize handler
    let resizeTimeout;
    window.addEventListener('resize', () => {
        clearTimeout(resizeTimeout);
        resizeTimeout = setTimeout(handleResize, 100);
    });

    // Keyboard navigation
    document.addEventListener('keydown', handleKeyboardNavigation);
}

// Language switching functionality
function handleLanguageSwitch(e) {
    const newLang = e.target.dataset.lang;
    if (!newLang || newLang === currentLang) return;
    
    if (!translations[newLang]) {
        console.error(`Translation for language "${newLang}" not found.`);
        showMessage('Language not supported.', 'error');
        return;
    }

    currentLang = newLang;
    updateLanguage(newLang);
    updateActiveLanguageButton(e.target);
}

function updateLanguage(lang) {
    document.querySelectorAll('[data-i18n]').forEach(element => {
        const key = element.dataset.i18n;
        element.textContent = translations[lang][key] || `Missing translation: ${key}`;
    });

    document.querySelectorAll('[data-i18n-placeholder]').forEach(btn => {
        const key = btn.dataset.i18nPlaceholder;
        btn.placeholder = translations[lang][key] || `Placeholder missing: ${key}`;
    });

    // Update About section
    if (elements.aboutTitle) elements.aboutTitle.textContent = translations[lang]['about.title'];
    if (elements.aboutDescription) elements.aboutDescription.textContent = translations[lang]['about.description'];
    if (elements.aboutFeatures?.length >= 3) {
        elements.aboutFeatures[0].textContent = translations[lang]['about.feature1'];
        elements.aboutFeatures[1].textContent = translations[lang]['about.feature2'];
        elements.aboutFeatures[2].textContent = translations[lang]['about.feature3'];
    }
    if (elements.aboutFeatureDescriptions?.length >= 3) {
        elements.aboutFeatureDescriptions[0].textContent = translations[lang]['about.feature1_desc'];
        elements.aboutFeatureDescriptions[1].textContent = translations[lang]['about.feature2_desc'];
        elements.aboutFeatureDescriptions[2].textContent = translations[lang]['about.feature3_desc'];
    }

    document.documentElement.lang = lang;
}

function updateActiveLanguageButton(activeBtn) {
    elements.langButtons.forEach(btn => {
        btn.classList.remove('active');
        btn.setAttribute('aria-pressed', 'false');
    });
    activeBtn.classList.add('active');
    activeBtn.setAttribute('aria-pressed', 'true');
}

// Search functionality
function handleSearch() {
    const searchTerm = elements.searchBar.value.trim().toLowerCase();
    if (!searchTerm) {
        showMessage(translations[currentLang]['search.empty'], 'error');
        return;
    }

    const activeTab = document.querySelector('.category-tab.active');
    if (!activeTab) {
        showMessage('No category selected.', 'error');
        return;
    }

    const activeCategory = activeTab.dataset.category;
    const productCards = document.querySelectorAll(`#${activeCategory}-section .product-card`);
    let foundResults = false;

    productCards.forEach(card => {
        const productName = card.querySelector('.product-name').textContent.toLowerCase();
        const isMatch = productName.includes(searchTerm);
        card.style.display = isMatch ? 'block' : 'none';
        if (isMatch) {
            card.scrollIntoView({ behavior: 'smooth', block: 'center' });
            foundResults = true;
        }
    });

    if (!foundResults) {
        showMessage(translations[currentLang]['search.no_results'].replace('%s', searchTerm), 'info');
        setTimeout(() => {
            productCards.forEach(card => card.style.display = 'block');
        }, 3000);
    }
}

// Mobile menu functionality
function toggleMobileMenu() {
    const isOpen = elements.navMenu.classList.contains('active');
    elements.navMenu.classList.toggle('active');
    elements.body.classList.toggle('menu-open');
    elements.mobileMenuBtn.setAttribute('aria-expanded', !isOpen);
    elements.mobileMenuBtn.innerHTML = isOpen ? '☰' : '✕';

    if (!isOpen) {
        trapFocus(elements.navMenu);
    } else {
        removeFocusTrap(elements.navMenu);
    }
}

// Trap focus within an element
function trapFocus(element) {
    const focusableElements = element.querySelectorAll('a, button, input, [tabindex="0"]');
    if (!focusableElements.length) return;

    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];

    const trap = e => {
        if (e.key === 'Tab') {
            if (e.shiftKey && document.activeElement === firstFocusable) {
                e.preventDefault();
                lastFocusable.focus();
            } else if (!e.shiftKey && document.activeElement === lastFocusable) {
                e.preventDefault();
                firstFocusable.focus();
            }
        }
    };

    element.addEventListener('keydown', trap);
    element.focusTrap = firstFocusable;
    focus();
}

// Focus trap removal
function removeFocusTrap(element) {
    if (element.focusTrap) {
        element.removeEventListener('keydown', element.focusTrap);
        delete element.focusTrap;
    }
}

// Navigation click handler
function handleNavClick(e) {
    e.preventDefault();
    const targetId = e.target.getAttribute('href');
    if (targetId === '#') return;

    const targetSection = document.querySelector(targetId);
    if (targetSection) {
        targetSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
        elements.navLinks.forEach(link => link.classList.remove('active'));
        e.target.classList.add('active');
        if (elements.navMenu.classList.contains('active')) toggleMobileMenu();
    }
}

// Category tab click handler
function handleCategoryTabClick() {
    const category = this.dataset.category;
    const targetSection = document.getElementById(`${category}-section`);
    if (!targetSection) return;

    document.querySelectorAll('.category-section').forEach(section => section.classList.add('hidden'));
    targetSection.classList.remove('hidden');
    
    elements.categoryTabs.forEach(tab => {
        tab.classList.remove('active');
        tab.setAttribute('aria-selected', 'false');
    });
    this.classList.add('active');
    this.setAttribute('aria-selected', 'true');

    document.getElementById('products').scrollIntoView({ behavior: 'smooth' });
}

// View details handler
function handleViewDetails(e) {
    const productCard = e.target.closest('.product-card');
    if (!productCard) return;

    const productId = productCard.dataset.productId;
    const productName = productCard.querySelector('.product-name').textContent;
    const productImageSrc = productCard.querySelector('.product-image img').src;
    const productImageAlt = productCard.querySelector('.product-image img').alt;
    const descriptionKey = `products.${productId}_desc`;

    if (!elements.modalTitle || !elements.modalImage) {
        console.error('Modal elements missing.');
        return;
    }

    elements.modalTitle.textContent = productName;
    elements.modalImage.src = productImageSrc;
    elements.modalImage.alt = productImageAlt;

    let modalDescription = document.querySelector('.modal-description');
    if (!modalDescription) {
        modalDescription = document.createElement('p');
        modalDescription.classList.add('modal-description');
        elements.productModal.querySelector('.modal-content').insertBefore(modalDescription, elements.modalCloseButtons[0]);
    }
    modalDescription.textContent = translations[currentLang][descriptionKey] || 'Description not available.';

    elements.productModal.classList.add('active');
    elements.body.classList.add('modal-open');

    trapFocus(elements.productModal);
    elements.modalCloseButtons[0].focus();
}

// Close product modal
function closeProductModal() {
    if (!elements.productModal) return;

    elements.productModal.classList.remove('active');
    elements.body.classList.remove('modal-open');
    removeFocusTrap(elements.productModal);
}

// Modal focus trap
function handleModalFocusTrap(e) {
    const focusableElements = elements.productModal.querySelectorAll('button, [tabindex="0"]');
    if (!focusableElements.length) return;

    const firstFocusable = focusableElements[0];
    const lastFocusable = focusableElements[focusableElements.length - 1];

    if (e.key === 'Tab') {
        if (e.shiftKey && document.activeElement === firstFocusable) {
            e.preventDefault();
            lastFocusable.focus();
        } else if (!e.shiftKey && document.activeElement === lastFocusable) {
            e.preventDefault();
            firstFocusable.focus();
        }
    }
}

// Generic message display
function showMessage(message, type) {
    const messageEl = document.createElement('div');
    messageEl.textContent = message;
    messageEl.style.cssText = `
        position: fixed;
        top: 16px;
        right: 16px;
        background: ${type === 'error' ? '#ef4444' : type === 'success' ? '#10b981' : '#3b82f6'};
        color: white;
        padding: 10px 16px;
        border-radius: 6px;
        z-index: 10000;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        transform: translateX(100%);
        transition: transform 0.3s ease;
        font-size: 0.9rem;
        max-width: 90%;
    `;
    
    document.body.appendChild(messageEl);
    
    setTimeout(() => messageEl.style.transform = 'translateX(0)', 100);
    
    setTimeout(() => {
        messageEl.style.transform = 'translateX(100%)';
        setTimeout(() => messageEl.remove(), 300);
    }, 3000);
}

// Intersection Observer for animations
function setupIntersectionObserver() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.1, rootMargin: '0px 0px -50px 0px' });
    
    document.querySelectorAll('.product-card, .feature-item, .category-section').forEach(el => observer.observe(el));
}

// Smooth scrolling setup
function setupSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(link => {
        link.addEventListener('click', e => {
            const href = link.getAttribute('href');
            if (href === '#') return;
            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
}

// Handle window resize
function handleResize() {
    if (window.innerWidth >= 768 && elements.navMenu?.classList.contains('active')) {
        toggleMobileMenu();
    }
}

// Keyboard navigation
function handleKeyboardNavigation(e) {
    if (e.key === 'Escape' && elements.navMenu?.classList.contains('active')) {
        toggleMobileMenu();
    }
}

// Performance optimizations
function optimizeImages() {
    document.querySelectorAll('img').forEach(img => img.loading = 'lazy');
}

// Service Worker for caching (commented out unless sw.js exists)
// if ('serviceWorker' in navigator) {
//     window.addEventListener('load', () => {
//         navigator.serviceWorker.register('/sw.js').catch(err => {
//             console.error('Service Worker registration failed:', err);
//         });
//     });
// }

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}