<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Responsive AppBar Test - Madjour Industries</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="styles.css?v=1.1">
    <style>
        /* Test styles to visualize different screen sizes */
        .test-container {
            padding: 2rem;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .test-section {
            background: white;
            padding: 2rem;
            margin: 2rem 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .screen-size-indicator {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--primary-red);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-weight: bold;
            z-index: 10000;
        }
        
        .test-product {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            text-align: center;
        }
        
        .test-product img {
            width: 100px;
            height: 100px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .test-product h4 {
            color: var(--dark-gray);
            margin: 0.5rem 0;
        }
        
        @media (max-width: 1024px) {
            .screen-size-indicator::after {
                content: " - Tablet";
            }
        }
        
        @media (max-width: 768px) {
            .screen-size-indicator::after {
                content: " - Mobile";
            }
        }
        
        @media (max-width: 480px) {
            .screen-size-indicator::after {
                content: " - Small Mobile";
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <div class="header-top">
            <div class="header-content">
                <div class="logo">
                    <img src="img/logo/logo madjour.png" alt="MADJOUR Logo" width="60" height="60" loading="lazy">
                </div>
                <div class="header-right">
                    <div class="language-switch">
                        <button class="lang-btn" data-lang="en">EN</button>
                        <button class="lang-btn active" data-lang="ar">AR</button>
                    </div>
                    <div class="search-container">
                        <input type="text" class="search-bar" placeholder="Search products..." data-i18n-placeholder="search.placeholder">
                        <button class="search-btn"><span class="material-icons">search</span></button>
                    </div>
                </div>
            </div>
        </div>
        <nav class="nav">
            <div class="nav-container">
                <button class="mobile-menu-btn" aria-label="Toggle Menu">☰</button>
                <ul class="nav-menu">
                    <!-- Language switcher for mobile menu -->
                    <li class="mobile-language-switch">
                        <button class="lang-btn" data-lang="en">EN</button>
                        <button class="lang-btn active" data-lang="ar">AR</button>
                    </li>
                    <li class="nav-item"><a href="#home" class="nav-link active">Home</a></li>
                    <li class="nav-item"><a href="#categories" class="nav-link">Categories</a></li>
                    <li class="nav-item"><a href="#products" class="nav-link">Products</a></li>
                    <li class="nav-item"><a href="#about" class="nav-link">About Us</a></li>
                    <li class="nav-item"><a href="#contact" class="nav-link">Contact</a></li>
                </ul>
            </div>
        </nav>
    </header>

    <div class="test-container">
        <div class="test-section">
            <h2>Responsive AppBar Test</h2>
            <p>This page tests the responsive behavior of the AppBar across different screen sizes:</p>
            <ul>
                <li><strong>Desktop (>1024px):</strong> Logo + search bar + language buttons in header (AR/EN not in nav)</li>
                <li><strong>Tablet (768px-1024px):</strong> Smaller logo, compact search bar, language buttons in header</li>
                <li><strong>Mobile (≤768px):</strong> Logo hidden, language buttons moved to mobile menu only</li>
                <li><strong>Small Mobile (≤480px):</strong> Extra compact layout</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>Test Product Modal - Centered Title</h3>
            <div class="test-product">
                <img src="img/logo/logo madjour.png" alt="Test Product">
                <h4>Test Product with Very Long Name That Should Wrap Properly</h4>
                <button class="view-details-btn" data-product-id="test" data-description-en="This is a test product with a long description to test how the modal handles text wrapping and responsive behavior on different screen sizes. The title should be perfectly centered." data-description-ar="هذا منتج تجريبي بوصف طويل لاختبار كيفية تعامل النافذة المنبثقة مع التفاف النص والسلوك المتجاوب على أحجام الشاشات المختلفة. يجب أن يكون العنوان في المنتصف تماماً.">View Details</button>
            </div>
            <p><strong>✅ New:</strong> Product names are now perfectly centered in modal cards!</p>
        </div>
    </div>

    <!-- Product Modal -->
    <div class="product-modal" id="product-modal">
        <div class="modal-content">
            <button class="modal-close-btn" aria-label="Close Modal">×</button>
            <h3 class="modal-title"></h3>
            <div class="modal-image"><img class="modal-product-image" src="" alt=""></div>
            <p class="modal-description"></p>
            <button class="modal-close-btn secondary">Close</button>
        </div>
    </div>

    <div class="screen-size-indicator">
        Screen Size: <span id="screen-width"></span>px
    </div>

    <script src="script.js"></script>
    <script src="responsive-tester.js"></script>
    <script>
        // Update screen size indicator
        function updateScreenSize() {
            document.getElementById('screen-width').textContent = window.innerWidth;
        }

        window.addEventListener('resize', updateScreenSize);
        updateScreenSize();
        
        // Test modal functionality
        document.querySelector('.view-details-btn').addEventListener('click', function() {
            const modal = document.getElementById('product-modal');
            const title = document.querySelector('.modal-title');
            const image = document.querySelector('.modal-product-image');
            const description = document.querySelector('.modal-description');
            
            title.textContent = 'Test Product with Very Long Name That Should Wrap Properly';
            image.src = 'img/logo/logo madjour.png';
            image.alt = 'Test Product';
            description.textContent = this.dataset.descriptionEn;
            
            modal.classList.add('active');
        });
    </script>
</body>
</html>
